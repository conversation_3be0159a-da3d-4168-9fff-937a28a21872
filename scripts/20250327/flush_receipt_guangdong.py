"""
    配置文件 广东省医疗收费票据-财政发票 -- 需要在配置一个收据的数据
"""

import argparse
import os
import sys
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

def run(chain_id, region_name, env):
    basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    property_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic' if env == 'dev' or env == 'test' else 'abc_cis_property', env, True)

    # 查询连锁下的门店
    clinicList = get_clinic_list(basic_db_client, chain_id)
    if len(clinicList) == 0:
        return
    # 1.查询 广东省医疗收费票据-财政发票 自定义配置的门店id
    clinicIdList = get_property_config(property_db_client, clinicList)
    if clinicIdList is None or len(clinicIdList) == 0:
        return
    for clinicId in clinicIdList:
        insertSQL = """
            INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, key_fifth, v2_scope_id)
            VALUES (uuid_short(), 'print.receipt.outpatient.format', '"guangdong"', 'clinic', null, 0, '00000000000000000000000000000000', CURRENT_TIMESTAMP, '00000000000000000000000000000000', CURRENT_TIMESTAMP, 'print', 'receipt', 'outpatient', 'format', null, '{ClinicId}'),
            (uuid_short(), 'print.receipt.outpatient.guangdong.institutionName', '"广东省医疗收费收据"', 'clinic', null, 0, '00000000000000000000000000000000', CURRENT_TIMESTAMP, '00000000000000000000000000000000', CURRENT_TIMESTAMP, 'print', 'receipt', 'outpatient', 'guangdong', 'institutionName', '{ClinicId}')
        """.format(ClinicId = clinicId['id'])
        property_db_client.execute(insertSQL)

"""
    查询连锁下的门店
"""
def get_clinic_list(db_client, chainId):
    sql = """
        select distinct id from organ where parent_id = '{chainId}' and status = 1
    """.format(chainId = chainId)
    clinicList = db_client.fetchall(sql)
    return clinicList

"""
    查询 广东省医疗收费票据-财政发票 自定义配置的门店id
"""
def get_property_config(db_client, clinicIdList):
    scopeIdList = db_client.fetchall("""
            select distinct v2_scope_id as id from v2_property_config_item 
            where `key` = 'print.bill.format' and value = '"guangdong"' and v2_scope_id in ({clinicIds})
    """.format(clinicIds = ','.join(f"'{clinicId['id']}'" for clinicId in clinicIdList)))
    return scopeIdList

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)
    run(args.chain_id, args.region_name, args.env)
#    run('41e2851df6034f21a60a68be464cc0ed', 'ShangHai', 'test')

if __name__ == '__main__':
    main()