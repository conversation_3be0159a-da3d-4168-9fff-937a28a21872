# -*- coding: utf-8 -*-
"""
@name: flush_pharmacy_stat_first_in_date.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-04-23 13:54:45
"""
import json
import os
import sys
import argparse
from datetime import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from scripts.common.utils.sqls import SqlUtils
from scripts.common.utils.lists import ListUtils

env = 'prod'
default_id = '00000000000000000000000000000000'

limit = 50


class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
        self.ob_goods_db_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', env, True)

    def run(self):
        organ_list = self.basic_db_client.fetchall("""select * from organ where parent_id = '{chainId}' and status = 1 and his_type = 10""".format(chainId=self.chain_id))
        if organ_list is None or len(organ_list) == 0:
            return
        print('start flush:', self.chain_id)
        single = organ_list[0]['view_mode'] == 1
        for organ in organ_list:
            if single:
                if organ['id'] == self.chain_id:
                    continue
            print(organ['id'])
            self.flush_goods_stat_first_in_date(self.chain_id, organ['id'], 0, False)
        if not single:
            # 查询总部汇总
            print('summary:', self.chain_id)
            self.flush_goods_stat_first_in_date(self.chain_id, '', -1, True)

    def flush_goods_stat_first_in_date(self, chain_id, clinic_id, pharmacy_no, is_summary):
        last_stat_id = None
        while True:
            if last_stat_id is None:
                stat_list = self.ob_goods_db_client.fetchall("""
                select * from v2_goods_stat where chain_id = '{chainId}' and clinic_id = '{clinicId}' and es_inorder = 1 and pharmacy_no = {pharmacyNo} order by _id limit {limit};
                """.format(chainId=chain_id, clinicId=clinic_id, pharmacyNo=pharmacy_no, limit=limit))
            else:
                stat_list = self.ob_goods_db_client.fetchall("""
                select * from v2_goods_stat where chain_id = '{chainId}' and clinic_id = '{clinicId}' and es_inorder = 1 and pharmacy_no = {pharmacyNo} and _id > '{lastStatId}' order by _id limit {limit};
                """.format(chainId=chain_id, clinicId=clinic_id, lastStatId=last_stat_id, pharmacyNo=pharmacy_no, limit=limit))
            if stat_list is None or len(stat_list) == 0:
                break
            last_stat_id = stat_list[-1]['_id']
            self.update_goods_stat_first_in_date(stat_list, is_summary, chain_id, clinic_id)

    def update_goods_stat_first_in_date(self, stat_list, is_summary, chain_id, clinic_id):
        if stat_list is None or len(stat_list) == 0:
            return
        goods_id_list = ListUtils.dist_mapping(stat_list, lambda x: x['goods_id'])
        goods_id_in_sql = f" goods_id in ({SqlUtils.to_in_value(goods_id_list)}) "
        if not is_summary:
            goods_stock_list = self.goods_db_client.fetchall("""
            select goods_id, min(created_date) create_date from v2_goods_stock where chain_id = '{chainId}' and organ_id = '{clinicId}' and {goodsIdInSql} 
            group by goods_id;
            """.format(chainId=chain_id, clinicId=clinic_id, goodsIdInSql=goods_id_in_sql))
        else:
            goods_stock_list = self.goods_db_client.fetchall("""
            select goods_id, min(created_date) create_date from v2_goods_stock where chain_id = '{chainId}' and {goodsIdInSql} 
            group by goods_id;
            """.format(chainId=chain_id, goodsIdInSql=goods_id_in_sql))
        if goods_stock_list is None or len(goods_stock_list) == 0:
            return
        goods_id_to_stock = ListUtils.to_map(goods_stock_list, lambda x: x['goods_id'])
        stat_id_list = []
        case_statement_list = []
        first_in_date_list = []
        for stat in stat_list:
            stock = goods_id_to_stock[stat['goods_id']]
            if stock is None:
                continue
            first_in_date = stat['first_in_date']
            if first_in_date and first_in_date < stock['create_date']:
                continue
            stat_id_list.append(stat['_id'])
            first_in_date_list.append(stock['create_date'])
            case_statement_list.append(f"when _id = {stat['_id']} then '{stock['create_date']}'")
            # update_sql = """
            # update v2_goods_stat set first_in_date = '{firstInDate}' where _id = {statId};
            # """.format(firstInDate=stock['create_date'], statId=stat['_id'])
            # print(update_sql)
        if len(stat_id_list) == 0:
            return
        SqlUtils.to_in_value(stat_id_list)
        update_sql = """
        UPDATE v2_goods_stat 
        SET first_in_date = CASE 
            {case_statements}
            ELSE first_in_date
        END
        WHERE _id IN ({stat_ids});
        """.format(
            case_statements=' '.join(case_statement_list),
            stat_ids=','.join(f"{e}"for e in stat_id_list)
        )
        # print(update_sql)
        self.goods_db_client.execute(update_sql)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
