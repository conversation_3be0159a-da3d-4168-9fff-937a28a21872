import argparse
import subprocess
import os

# shebao执行脚本程序入口，jenkins上配置这个脚本，外部输入参数
def main():
    parser = argparse.ArgumentParser(description='Execute a specified Python script.')
    parser.add_argument('script_path', type=str, help='The path to the Python script to execute.')
    parser.add_argument('--env', help='环境dev/test/prod')
    parser.add_argument('--regionName', help='ShangHai/HangZhou')
    parser.add_argument('--chainId', help='chainId', default='')
    parser.add_argument('--clinicId', help='clinicId', default='')
    parser.add_argument('--beginDate', help='beginDate', default= '')
    parser.add_argument('--endDate', help='endDate', default='')
    parser.add_argument('--month', help='month', default='')
    args = parser.parse_args()

    # Debug: Manually set args for testing
    # args.script_path = 'shebao_update_sell_no.py'
    # args.env = 'dev'
    # args.regionName = 'ShangHai'
    # args.chainId = ''
    # args.clinicId = ''
    # args.beginDate = '2025-01-01'
    # args.endDate = '2025-06-01'
    # args.month = '2025-05'

    script_path = args.script_path

    if not os.path.isfile(script_path):
        print(f"Error: The script {script_path} does not exist.")
        return
    command = ['python3', script_path]
    if args.env:
        command.extend(['--env', args.env])
    if args.regionName:
        command.extend(['--regionName', args.regionName])
    if args.chainId:
        command.extend(['--chainId', args.chainId])
    if args.clinicId:
        command.extend(['--clinicId', args.clinicId])
    if args.beginDate:
        command.extend(['--beginDate', args.beginDate])
    if args.endDate:
        command.extend(['--endDate', args.endDate])
    if args.month:
        command.extend(['--month', args.month])

    try:
        subprocess.run(command, check=True)
    except subprocess.CalledProcessError as e:
        print(f"An error occurred while executing the script: {e}")


if __name__ == '__main__':
    main()
