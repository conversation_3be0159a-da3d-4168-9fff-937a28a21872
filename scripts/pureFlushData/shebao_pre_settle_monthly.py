#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import json
import requests


from multizone.db import DBClient

# 开通医保合规
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chainId', help='连锁id')
    parser.add_argument('--clinicId', help='门店id')
    parser.add_argument('--month', help='月份 yyyy-MM')
    parser.add_argument('--regionName', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境dev/test/prod')
    args = parser.parse_args()

    if not args.regionName or not args.env:
        parser.print_help()
        sys.exit(-1)

    apiUrlPrefix = ""
    if args.env == "dev":
        apiUrlPrefix = "dev.rpc.abczs.cn"
    elif args.env == "test":
        apiUrlPrefix = "test.rpc.abczs.cn"
    elif args.env == "prod":
        apiUrlPrefix = "pre.rpc.abczs.cn"


    if args.regionName == "HangZhou":
        apiUrlPrefix = "region2-pre.rpc.abczs.cn"

    requestUrl = "http://" + apiUrlPrefix + "/rpc/shebao-settle/pre-settle-monthly"
    # 设置请求头
    headers = {"content-type": "application/json"}

    # 设置请求体（JSON 参数）
    payload = {
        "clinicId": args.clinicId,
        "chainId": args.chainId,
        "month": args.month
    }
    print ("请求 requestUrl:" + requestUrl)
    r = requests.post(requestUrl, headers=headers, data=json.dumps(payload))
    print ("同步 url output:" + str(r.status_code))
    print ("同步 结果 rsp content = " + r.text)
    if r.status_code != 200:
        raise Exception("同步 失败")
if __name__ == '__main__':
    main()
