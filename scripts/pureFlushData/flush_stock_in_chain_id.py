# -*- coding: utf-8 -*-
"""
@name: flush_stock_in_chain_id.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-05-06 18:16:40
"""
import json
import os
import sys
import argparse
import time
from datetime import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from scripts.common.utils.sqls import SqlUtils
from scripts.common.utils.lists import ListUtils

env = 'prod'
default_id = '00000000000000000000000000000000'

limit_count = 200


class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
        self.ob_goods_db_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', env, True)

    def run(self):
        organ = self.basic_db_client.fetchone("""select * from organ where id = '{chainId}' and status = 1""".format(chainId=self.chain_id))
        if organ is None:
            return
        if organ['his_type'] == 100 or organ['his_type'] == 10:
            return
        print(organ['id'])
        order_id = None
        update_count = 0
        while True:
            if order_id is None:
                order_id_list = self.ob_goods_db_client.fetchall("""
                select distinct a.id from v2_goods_stock_in_order a
                    join v2_goods_stock_in b on a.id = b.order_id and b.status = 1
                    where a.chain_id = '{chainId}' and (b.chain_id is null or b.chain_id = '')
                    and order_no like 'RK%'
                    order by a.id limit {limitCount}
                """.format(chainId=self.chain_id, limitCount=limit_count))
            else:
                order_id_list = self.ob_goods_db_client.fetchall("""
            select distinct a.id from v2_goods_stock_in_order a
                join v2_goods_stock_in b on a.id = b.order_id and b.status = 1
                where a.chain_id = '{chainId}' and (b.chain_id is null or b.chain_id = '')
                and order_no like 'RK%' and a.id > '{orderId}'
                order by a.id limit {limitCount}
                        """.format(chainId=self.chain_id, limitCount=limit_count, orderId=order_id))
            if len(order_id_list) == 0:
                break
            order_id = order_id_list[-1]['id']
            for order in order_id_list:
                update_sql = """
                update v2_goods_stock_in set chain_id = '{chainId}', original_in_id = id, original_in_order_id = order_id where order_id = {orderId} and (chain_id is null or chain_id = '');
                """.format(chainId=self.chain_id, orderId=order['id'])
                # print(update_sql)
                self.goods_db_client.execute(update_sql)
            time.sleep(0.5)
        #         update_count += 1
        # print(update_count)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
