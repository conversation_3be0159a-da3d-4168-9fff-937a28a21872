"""
    刷门店默认字段配置数据信息
"""

import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient

"""
    chain_id: 门店id
    region_name: 分区
    env: 环境
"""
def run(chain_id_input, region_name, env):
    basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    property_db_client = DBClient(region_name, 'abc_cis_mixed','abc_cis_property', env, True)
    redis_client = RedisClient(region_name, 'abc-ha-redis', 13, env, True)

    clinicList = get_clinic_list(basic_db_client, chain_id_input)
    if len(clinicList) == 0:
        return

    # 获取该连锁关联的所有门店医生
    employeeSql = '''
          select distinct b.employee_id
      from organ a
               inner join clinic_employee b on a.id = b.chain_id
      where a.id = '{chain_id}' and b.status = 1 and a.status = 1
      '''
    clinicEmployee = basic_db_client.fetchall(employeeSql.format(chain_id=chain_id_input))
    if len(clinicEmployee) == 0:
        return

    formatEmployeeIds = ','.join(f"'{ce['employee_id']}'" for ce in clinicEmployee)
    # 员工配置
    insertSql3 = '''
            insert into v2_property_config_item (id, `key`, value, scope, scope_id, created_by, last_modified_by,
                                                               key_first, key_second, key_third, key_fourth, key_fifth,
                                                               v2_scope_id)
                select substr(uuid_short(), 4),
                       replace(`key`, 'tongue', 'chineseExamination'),
                       value,
                       scope,
                       scope_id,
                       created_by,
                       last_modified_by,
                       key_first,
                       key_second,
                       'chineseExamination',
                       key_fourth,
                       key_fifth,
                       v2_scope_id
                from v2_property_config_item
                where scope = 'employee' and key_third = 'tongue' and key_first = 'outpatient'
                  and v2_scope_id in ({employeeIds}) and value = 1
                ON DUPLICATE KEY UPDATE value = values(value)
            '''.format(str=str, employeeIds=formatEmployeeIds)

    insertSql4 = '''
                insert into v2_property_config_item (id, `key`, value, scope, scope_id, created_by, last_modified_by,
                                                                   key_first, key_second, key_third, key_fourth, key_fifth,
                                                                   v2_scope_id)
                    select substr(uuid_short(), 4),
                           replace(`key`, 'pluse', 'chineseExamination'),
                           value,
                           scope,
                           scope_id,
                           created_by,
                           last_modified_by,
                           key_first,
                           key_second,
                           'chineseExamination',
                           key_fourth,
                           key_fifth,
                           v2_scope_id
                    from v2_property_config_item
                    where scope = 'employee' and key_third = 'pluse' and key_first = 'outpatient'
                      and v2_scope_id in ({employeeIds}) and value = 1
                    ON DUPLICATE KEY UPDATE value = values(value)
                '''.format(str=str, employeeIds=formatEmployeeIds)

    row_count = property_db_client.execute(insertSql3) + property_db_client.execute(insertSql4)
    print("affected rows: {}".format(row_count))

    # clear clinic key
    for clinic in clinicList:
        clean_redis_key = 'field'
        while len(clean_redis_key) > 0:
            clinicId = clinic['id']
            delResult = redis_client.client.delete(f'property.v3.config.item.clinic.{clinicId}.{clean_redis_key}.v1')
            print("del clinic key  {} cache result: {}".format(f'property.v3.config.item.clinic.{clinicId}.{clean_redis_key}.v1', delResult))
            if clean_redis_key.rfind('.') == -1:
                break
            clean_redis_key = clean_redis_key[:clean_redis_key.rfind('.')]

    # clear employee key
    for ce in clinicEmployee:
        clean_redis_key1 = 'outpatient'
        employeeId = ce['employee_id']
        delResult = redis_client.client.delete(f'property.v3.config.item.employee.{employeeId}.{clean_redis_key1}.v1')
        print("del employee key  {} cache result: {}".format(
            f'property.v3.config.item.clinic.{employeeId}.{clean_redis_key1}.v1', delResult))


"""
    查询连锁下的门店
"""
def get_clinic_list(db_client, chainId):
    sql = """
        select distinct id from organ where parent_id = '{chainId}' and status = 1
    """.format(chainId=chainId)
    clinicList = db_client.fetchall(sql)
    return clinicList


"""
    查询Key必填的连锁id
"""


def get_property_config(db_client, configKey):
    sql = """
        select distinct v2_scope_id as id from v2_property_config_item where `key` = '{configKey}' and value = '1'
    """.format(configKey=configKey)
    chainIdList = db_client.fetchall(sql)
    return chainIdList


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)
    run(args.chain_id, args.region_name, args.env)

    # basic_db_client = DBClient("ShangHai", 'abc_cis_mixed', 'abc_cis_basic', "test", True)
    # organs = basic_db_client.fetchall("""
    #     select id
    #     from organ
    #     where id = parent_id
    # """)
    # if not organs:
    #     return
    #
    # for organ in organs:
    #     run(organ['id'], 'ShangHai', 'test')

    # run('628f2c02d90c480fa26fbed3d579ebc2', 'ShangHai', 'test')


if __name__ == '__main__':
    main()
