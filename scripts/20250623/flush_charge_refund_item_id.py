# -*- coding: utf-8 -*-
"""
@name: flush_charge_refund_item_id.py
@author: y<PERSON><PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-05-15 16:56:53
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils


class UpdateData:

    def __init__(self, region_name, env, chain_id):
        self.stock_db_client = None
        self.dispensing_db_client = None
        self.chain_id = chain_id
        self.region_name = region_name
        self.env = env
        self.db_suffix = self.get_db_suffix()
        # 初始化数据库连接
        self.ob_client = DBClient(self.region_name, 'ob', 'abc_cis_basic', env, True)

    def run(self):
        """执行刷数据主流程"""
        # 只处理药店
        if not self.is_pharmacy():
            return

        self.dispensing_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_dispensing', self.env, True)
        if self.env == 'dev' or self.env == 'test':
            self.stock_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)
        else:
            self.stock_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', self.env, True)

        charge_sheet_id = None
        charge_sheet_count = 0
        empty_charge_item_count = 0
        update_dispensing_sheet_count = 0
        error_dispensing_sheet_count = 0
        update_item_count = 0
        while True:
            # 查询发生过退费的收费单ID
            charge_sheet_ids = self.query_charge_sheet(charge_sheet_id, 100)
            if not charge_sheet_ids or len(charge_sheet_ids) == 0:
                break

            charge_sheet_id = charge_sheet_ids[-1]

            # 查询退费项
            all_refund_charge_items = self.query_refund_charge_items(charge_sheet_ids)

            # 查询退药项
            all_undispensing_items = self.query_undispensing_items(charge_sheet_ids)

            charge_sheet_id_to_refund_charge_items = ListUtils.group_by(all_refund_charge_items, lambda x: x['charge_sheet_id'])

            charge_sheet_id_to_undispensing_items = ListUtils.group_by(all_undispensing_items, lambda x: x['charge_sheet_id'])

            charge_sheet_count += len(charge_sheet_ids)

            for charge_sheet_id in charge_sheet_ids:
                # 获取当前收费单的所有退费项
                refund_charge_items = charge_sheet_id_to_refund_charge_items.get(charge_sheet_id, [])

                # 获取当前收费单的所有退药项
                undispensing_items = charge_sheet_id_to_undispensing_items.get(charge_sheet_id, [])
                if len(undispensing_items) == 0 and len(refund_charge_items) == 0:
                    empty_charge_item_count += 1
                    continue

                # 如果不一致，则打印日志
                if len(refund_charge_items) != len(undispensing_items):
                    print(f"收费单 {charge_sheet_id} 的退费项数量 {len(refund_charge_items)} 和退药项数量 {len(undispensing_items)} 不一致")
                    error_dispensing_sheet_count += 1
                    continue

                # refund_charge_items 和 undispensing_items 都按照 goods_id、created、id 排序
                refund_charge_items = sorted(refund_charge_items, key=lambda x: (x['product_id'], x['created'], x['id']))
                undispensing_items = sorted(undispensing_items, key=lambda x: (x['product_id'], x['created'], x['id']))

                process_undispensing_item_ids = []
                has_error = False
                undispensing_item_id_to_refund_item_id = {}
                bat_id_to_refund_item_id = {}
                for refund_charge_item in refund_charge_items:
                    charge_form_item_id = refund_charge_item['associate_form_item_id']
                    # 从 undispensing_items 中找到第一个退药项，unit_count 和 dose_count 相同，如果已经匹配过，则跳过
                    undispensing_item = None
                    for undispensing_item in undispensing_items:
                        if undispensing_item['id'] in process_undispensing_item_ids:
                            continue
                        if undispensing_item['source_form_item_id'] == charge_form_item_id and \
                                undispensing_item['unit_count'] == refund_charge_item['unit_count'] and \
                                undispensing_item['dose_count'] == refund_charge_item['dose_count']:
                            break

                    if undispensing_item is None:
                        # 如果没有找到，则打印日志
                        print(f"收费单 {charge_sheet_id} 的退费项 {refund_charge_item['id']} 没有找到对应的退药项")
                        has_error = True
                        break

                    process_undispensing_item_ids.append(undispensing_item['id'])
                    undispensing_item_id_to_refund_item_id[undispensing_item['id']] = refund_charge_item['id']
                    bat_id_to_refund_item_id[undispensing_item['bat_id']] = refund_charge_item['id']

                if has_error:
                    error_dispensing_sheet_count += 1
                    continue

                update_dispensing_sheet_count += 1
                update_item_count = update_item_count + len(undispensing_item_id_to_refund_item_id)

                # 如果没有错误，则更新退药项的 source_refund_form_item_id 字段
                for undispensing_item_id in undispensing_item_id_to_refund_item_id:
                    refund_item_id = undispensing_item_id_to_refund_item_id[undispensing_item_id]
                    self.dispensing_db_client.execute("""update v2_dispensing_form_item set source_refund_form_item_id = '{refund_item_id}' where id = '{undispensing_item_id}';"""
                                                      .format(refund_item_id=refund_item_id, undispensing_item_id=undispensing_item_id))

                # 更新进销存记录的 source_refund_order_detail_id 字段
                for bat_id in bat_id_to_refund_item_id:
                    undispnsing_item_id = bat_id_to_refund_item_id[bat_id]
                    self.stock_db_client.execute("""update v2_goods_stock_log set source_order_refund_detail_id = '{refund_item_id}' where bat_id = '{bat_id}';"""
                                                 .format(refund_item_id=undispnsing_item_id, bat_id=bat_id))

            print(
                f"连锁 {self.chain_id} "
                f"{charge_sheet_count} 个收费单 ，"
                f"{empty_charge_item_count} 个收费单没有退费项，"
                f"{update_dispensing_sheet_count} 个收费单更新了退药项，"
                f"{error_dispensing_sheet_count} 个收费单没有找到对应的退药项，"
                f"更新了 {update_item_count} 个退药项")

    def is_pharmacy(self):
        """判断是否为药店"""
        organ = self.ob_client.fetchone("""
            SELECT id FROM organ 
            WHERE id = '{chain_id}' 
            AND status = 1 
            AND his_type = 10
        """.format(chain_id=self.chain_id))

        return organ is not None

    def query_charge_sheet(self, charge_sheet_id, size):
        cursor = ''
        if charge_sheet_id:
            cursor = 'AND charge_sheet_id > \'{charge_sheet_id}\''.format(charge_sheet_id=charge_sheet_id)
        charge_sheets = self.ob_client.fetchall("""
            select distinct charge_sheet_id
            from abc_cis_charge{db_suffix}.v2_charge_action
            where chain_id = '{chain_id}'
              and type = 1
              {cursor}
            order by charge_sheet_id
            like {size}
    """.format(chain_id=self.chain_id, cursor=cursor, size=size, db_suffix=self.db_suffix))

        return ListUtils.dist_mapping(charge_sheets, lambda x: x['charge_sheet_id'])

    def query_refund_charge_items(self, charge_sheet_ids):
        if not charge_sheet_ids:
            return []

        charge_sheet_ids_str = SqlUtils.to_in_value(charge_sheet_ids)
        refund_charge_items = self.ob_client.fetchall("""
            SELECT id, charge_sheet_id, associate_form_item_id, product_id, unit_count, dose_count, created
            FROM abc_cis_charge{db_suffix}.v2_charge_form_item 
            WHERE charge_sheet_id IN ({charge_sheet_ids})
            AND is_deleted = 0
            AND associate_form_item_id IS NOT NULL 
            AND associate_form_item_id != ''
        """.format(charge_sheet_ids=charge_sheet_ids_str, db_suffix=self.db_suffix))

        return refund_charge_items

    def query_undispensing_items(self, charge_sheet_ids):
        if not charge_sheet_ids:
            return []

        charge_sheet_ids_str = SqlUtils.to_in_value(charge_sheet_ids)
        undispensing_items = self.dispensing_db_client.fetchall("""
            select b.id, a.source_sheet_id as 'charge_sheet_id', b.source_form_item_id, b.product_id, b.unit_count, b.stock_deal_id as 'bat_id', b.dose_count, b.created
            from abc_cis_dispensing{db_suffix}.v2_dispensing_sheet a
                     inner join abc_cis_dispensing{db_suffix}.v2_dispensing_form_item b on (a.id = b.dispensing_sheet_id)
            where a.source_sheet_id in ({charge_sheet_ids})
              and b.associate_form_item_id is not null
              and b.associate_form_item_id != '';
        """.format(charge_sheet_ids=charge_sheet_ids_str, db_suffix=self.db_suffix))

        return undispensing_items

    def get_db_suffix(self):
        """获取数据库后缀"""
        if self.env == 'prod' or self.env == 'pre' or self.env == 'gray':
            return ''
        elif self.env == 'test':
            return '_test'
        elif self.env == 'dev':
            return '_dev'
        else:
            raise ValueError('Invalid env: {}'.format(self.env))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.env, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
