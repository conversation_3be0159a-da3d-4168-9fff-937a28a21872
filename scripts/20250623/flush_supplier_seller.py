# -*- coding: utf-8 -*-
"""
@name: flush_goods_supplier_name_py.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-07-24 18:25:27
"""
import argparse
import json
import sys
import requests
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import os
from multizone.db import DBClient
from pypinyin import pinyin, lazy_pinyin, Style
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))


class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)

    def flush_supplier_seller(self):
        supplier_list = self.goods_db_client.fetchall("""
                  select *
                  from v2_goods_supplier
                  where is_deleted = 0 and principal is not null and principal != ''  and chain_id = '{chainId}'
            """.format(chainId=self.chain_id))
        if supplier_list is None:
            return
        for supplier in supplier_list:
            supplier_id = supplier['id']
            seller_name = supplier['principal']
            seller_id_card = supplier['principal_id_card']
            seller_mobile = supplier['principal_telephone'] if supplier['principal_telephone'] is not None else 'null'
            seller_id_card = supplier['principal_id_card'] if supplier['principal_id_card'] is not None else 'null'
            if supplier_id is None or seller_name is None or len(seller_name.strip()) == 0:
                continue
            insert_sql = """
                insert into v2_goods_supplier_seller(id, chain_id, supplier_id, name, mobile, id_card, created, created_by,
                                                     last_modified, last_modified_by)
                values (substr(uuid_short(),5), '{chain_id}', '{supplier_id}', '{seller_name}', if('{seller_mobile}' = 'null', null, '{seller_mobile}'), if('{seller_id_card}' = 'null', null, '{seller_id_card}'), now(), '{created_by}', now(), '{last_modified_by}')
            """.format(chain_id=self.chain_id, supplier_id=supplier_id, seller_name=seller_name,seller_mobile= seller_mobile, seller_id_card = seller_id_card,
            created_by = supplier['created_by'], last_modified_by = supplier['last_modified_by'])
            # print(update_sql)
            self.goods_db_client.execute(insert_sql)
        pass

    def run(self):
        self.flush_supplier_seller()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()