# -*- coding: utf-8 -*-
"""
@name: flush_pharmacy_supplier_entrust_delivery_type.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-05-26 08:55:19
"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import requests

default_id = '00000000000000000000000000000000'
env = 'prod'


def flushData(abcRegion, chain_id):
    goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', env, True)
    basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    organ = basic_db_client.fetchone(
        ''' select id as id,his_type as hisType from organ where id = '{chainId}' and his_type = 10 and status = 1'''.format(chainId=chain_id))
    if organ is None:
        return
    suppliers = goods_db_client.fetchall("""
    select * from v2_goods_supplier where chain_id = '{chainId}' and is_entrust_delivery = 1
    """.format(chainId=chain_id))
    if suppliers is None or len(suppliers) == 0:
        return
    print(chain_id)
    for supplier in suppliers:
        supplier_id = supplier['id']
        update_sql = """
        update v2_goods_supplier set entrust_delivery_type = 1 where id = '{supplierId}' and chain_id = '{chainId}';
        """.format(supplierId=supplier_id, chainId=chain_id)
        # print(update_sql)
        goods_db_client.execute(update_sql)


def run(abcRegion, chain_id):
    flushData(abcRegion, chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
