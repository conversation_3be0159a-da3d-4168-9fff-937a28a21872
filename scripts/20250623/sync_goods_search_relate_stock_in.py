# -*- coding: utf-8 -*-
import argparse
import os
import sys
import time

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from scripts.common.utils.sqls import SqlUtils

DEFAULT_ID = '00000000000000000000000000000000'


class UpdateData:
    env = None
    chain_id = None
    region_name = None
    goods_rdb_client = None
    goods_wdb_client = None

    def __init__(self, region_name, chain_id, env):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_rdb_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', env, True)
        self.goods_wdb_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)

    def run(self):
        self.do_sync_goods_stock_in()

    def do_sync_goods_stock_in(self):
        need_sync_stock_in_orders = self.goods_rdb_client.fetchall("""
            select a.id, count(b.id)
            from v2_goods_stock_in_order a
                     inner join v2_goods_stock_in b on (a.id = b.order_id)
            where a.chain_id = '{chainId}'
              and a.status = 2
              and b.status != 99
            group by a.id
            having count(b.id) > 100;
        """.format(chainId=self.chain_id))

        if not need_sync_stock_in_orders:
            print(f"连锁 {self.chain_id} 没有需要同步的入库单")
            return

        # 20 个一组，分组修改入库单的创建时间
        for i in range(0, len(need_sync_stock_in_orders), 20):
            order_ids = [order['id'] for order in need_sync_stock_in_orders[i:i + 20]]
            self.goods_wdb_client.execute("""
                update v2_goods_stock_in_order
                set created_date = DATE_ADD(created_date, interval 1 second)
                where id in ({order_ids});
            """.format(order_ids=SqlUtils.to_in_value(order_ids)))

            # 等待 2s
            time.sleep(2)
        print(f"连锁 {self.chain_id} 同步的入库单数量: {len(need_sync_stock_in_orders)}")


def main():
    parser = argparse.ArgumentParser(description='药店会员价刷数据脚本')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名称[ShangHai/HangZhou/...]')
    parser.add_argument('--env', help='环境[dev/test/prod]')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id, args.env)
    update_data.run()


if __name__ == '__main__':
    main()
