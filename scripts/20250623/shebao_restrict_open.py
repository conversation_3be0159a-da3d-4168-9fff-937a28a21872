#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import json
import requests


from multizone.db import DBClient

# 开通医保合规
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境dev/test/prod')
    args = parser.parse_args()
    # args.env = 'dev'
    # args.region_name = 'ShangHai'
    # args.chain_id = 'ffffffff0000000034a8b1f426718000'
    # args.clinicId = 'ffffffff0000000034a8b1f426718002'
    if not args.region_name or not args.env:
        parser.print_help()
        sys.exit(-1)


    shebao_ob_client = DBClient(args.region_name, 'ob', 'abc_cis_shebao', args.env, True)

    abc_cis_shebao = 'abc_cis_shebao'
    abc_cis_basic = 'abc_cis_basic'

    sqlQuery = f"""
         select a.clinic_id as clinic_id from {abc_cis_shebao}.shebao_clinic_config a inner join {abc_cis_basic}.organ b on a.clinic_id = b.id and b.status = 1 and his_type = 10 where a.chain_id = '{args.chain_id}' and a.status = 10 and a.region is not null;
        """
    res = shebao_ob_client.fetchall(sqlQuery)
    # 调用rpc
    print('sqlQuery:', sqlQuery)

    if len(res) == 0:
        print('res is empty')
        return

    apiUrlPrefix = ""
    if args.env == "dev":
        apiUrlPrefix = "dev.rpc.abczs.cn"
    elif args.env == "test":
        apiUrlPrefix = "test.rpc.abczs.cn"
    elif args.env == "prod":
        apiUrlPrefix = "pre.rpc.abczs.cn"


    if args.region_name == "HangZhou":
        apiUrlPrefix = "region2-pre.rpc.abczs.cn"

    requestUrl = "http://" + apiUrlPrefix + "/rpc/shebao-restrict/config/script"
    # 设置请求头
    headers = {"content-type": "application/json"}

    for update_sql in res:
        # 设置请求体（JSON 参数）
        payload = {
            "clinicId": update_sql['clinic_id'],
            "chainId": args.chain_id,
            "operateId": "system"
        }
        print ("请求 requestUrl:" + requestUrl)
        r = requests.post(requestUrl, headers=headers, data=json.dumps(payload))
        print ("同步 url output:" + str(r.status_code))
        print ("同步 结果 rsp content = " + r.text)
        if r.status_code != 200:
            raise Exception("同步 失败")
if __name__ == '__main__':
    main()
