# -*- coding: utf-8 -*-
"""
追溯码采集日志数据迁移

目标将 v2_goods_stock_traceable_code_log 表中的历史数据迁移到 v3_goods_stock_traceable_code_log 中

1. 判断当前门店是否开启的码上放心（开通了才迁移）
2. 按天查询当前连锁 v2_goods_stock_traceable_code_log 表中的数据 patientOrderId 数据（仅迁移有 patientOrderId 的数据）
3. 查询进销存日志 v2_goods_stock_log 表中 patient_order_id = patientOrderId 的数据（goods 字段需要有 traceableCodeList 字段）
4. 将数据回写到 v3_goods_stock_traceable_code_log 表中

"""
import argparse
import json
import os
import random
import sys
import time
from datetime import datetime, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


class TraceCodeMigrator:
    def __init__(self, region_name, chain_id, env='prod'):
        self.chain_id = chain_id
        self.region_name = region_name
        self.env = env

        # 初始化数据库连接
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)

        # 根据环境选择日志数据库
        goods_log_database = 'abc_cis_goods_log'
        if env == 'dev' or env == 'test':
            goods_log_database = 'abc_cis_goods'
        self.goods_log_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', goods_log_database, self.env, True)

        # 用于跟踪已处理的patient_order_id，避免重复处理
        self.processed_patient_order_ids = set()

        print(f"[INFO] 初始化迁移器 - 连锁ID: {chain_id}, 区域: {region_name}, 环境: {env}")

    def check_traceable_code_enabled(self):
        """检查连锁是否开启了码上放心功能"""
        try:
            chain_config = self.goods_db_client.fetchone("""
                SELECT chain_external_flag, open_pharmacy_flag
                FROM v2_goods_chain_config
                WHERE chain_id = '{chain_id}'
            """.format(chain_id=self.chain_id))

            if not chain_config:
                print(f"[WARNING] 连锁 {self.chain_id} 未找到配置信息")
                return False

            # 检查是否开启了追溯码相关功能（这里需要根据实际的标志位来判断）
            # 从代码中看到有各种标志位，需要确认具体哪个标志位代表码上放心
            chain_external_flag = chain_config.get('chain_external_flag', 0)
            open_pharmacy_flag = chain_config.get('open_pharmacy_flag', 0)

            # 假设码上放心功能通过某个标志位控制，这里先简单检查是否有配置
            is_enabled = chain_external_flag > 0 or open_pharmacy_flag > 0

            print(f"[INFO] 连锁 {self.chain_id} 码上放心功能状态: {'已开启' if is_enabled else '未开启'}")
            print(f"[DEBUG] chain_external_flag: {chain_external_flag}, open_pharmacy_flag: {open_pharmacy_flag}")

            return is_enabled

        except Exception as e:
            print(f"[ERROR] 检查码上放心功能状态失败: {str(e)}")
            return False

    def load_processed_patient_order_ids(self):
        """加载已经处理过的patient_order_id列表，避免重复处理"""
        try:
            sql = """
                SELECT DISTINCT patient_order_id
                FROM v3_goods_stock_traceable_code_log
                WHERE chain_id = '{chain_id}'
                  AND patient_order_id IS NOT NULL
                  AND patient_order_id != ''
            """.format(chain_id=self.chain_id)

            records = self.goods_db_client.fetchall(sql)
            if records:
                self.processed_patient_order_ids = {record['patient_order_id'] for record in records}
                print(f"[INFO] 加载了 {len(self.processed_patient_order_ids)} 个已处理的 patient_order_id")
            else:
                print("[INFO] 没有找到已处理的 patient_order_id")

        except Exception as e:
            print(f"[ERROR] 加载已处理的 patient_order_id 失败: {str(e)}")
            # 如果加载失败，继续执行，但可能会有重复处理

    def get_patient_order_ids_by_date(self, date_str):
        """按日期获取v2_goods_stock_traceable_code_log表中有patient_order_id的数据"""
        try:
            sql = """
                SELECT DISTINCT patient_order_id
                FROM v2_goods_stock_traceable_code_log
                WHERE chain_id = '{chain_id}'
                  AND DATE(created) = '{date_str}'
                  AND patient_order_id IS NOT NULL
                  AND patient_order_id != ''
            """.format(chain_id=self.chain_id, date_str=date_str)

            records = self.goods_db_client.fetchall(sql)
            all_patient_order_ids = [record['patient_order_id'] for record in records] if records else []

            # 过滤掉已经处理过的patient_order_id
            new_patient_order_ids = [
                pid for pid in all_patient_order_ids
                if pid not in self.processed_patient_order_ids
            ]

            print(f"[INFO] 日期 {date_str} 找到 {len(all_patient_order_ids)} 个 patient_order_id，其中 {len(new_patient_order_ids)} 个未处理")
            if len(all_patient_order_ids) > len(new_patient_order_ids):
                print(f"[INFO] 跳过了 {len(all_patient_order_ids) - len(new_patient_order_ids)} 个已处理的 patient_order_id")

            return new_patient_order_ids

        except Exception as e:
            print(f"[ERROR] 获取日期 {date_str} 的 patient_order_id 失败: {str(e)}")
            return []

    def get_stock_log_with_traceable_codes(self, patient_order_ids):
        """查询进销存日志中包含traceableCodeList的数据"""
        if not patient_order_ids:
            return []

        try:
            # 构建IN查询条件
            patient_order_ids_str = "','".join(patient_order_ids)

            sql = """
                SELECT id, chain_id, organ_id, goods_id, goods, action,
                       piece_num, piece_count, package_count, package_cost_price,
                       stock_piece_count, stock_package_count, stock_change_cost, stock_total_cost,
                       batch_piece_count, batch_package_count, batch_total_cost,
                       pharmacy_piece_count, pharmacy_package_count, pharmacy_total_cost,
                       goods_piece_count, goods_package_count, goods_total_cost,
                       bat_id, order_id, order_detail_id, batch_id, stock_id,
                       _in_tax_rat, pharmacy_no, supplier_id, created_date, created_user_id,
                       patient_order_id
                FROM v2_goods_stock_log
                WHERE patient_order_id IN ('{patient_order_ids}')
                  AND goods IS NOT NULL
                  AND JSON_VALID(goods) = 1
                  AND JSON_EXTRACT(goods, '$.traceableCodeList') IS NOT NULL
            """.format(patient_order_ids=patient_order_ids_str)

            records = self.goods_log_db_client.fetchall(sql)
            print(f"[INFO] 找到 {len(records)} 条包含 traceableCodeList 的进销存日志")
            return records

        except Exception as e:
            print(f"[ERROR] 查询进销存日志失败: {str(e)}")
            return []

    def extract_traceable_codes_from_goods(self, goods_json_str):
        """从goods字段中提取traceableCodeList"""
        try:
            if not goods_json_str:
                return []

            goods_data = json.loads(goods_json_str)
            traceable_code_list = goods_data.get('traceableCodeList', [])

            if not isinstance(traceable_code_list, list):
                return []

            return traceable_code_list

        except (json.JSONDecodeError, TypeError) as e:
            print(f"[WARNING] 解析goods字段失败: {str(e)}")
            return []

    def migrate_to_v3_table(self, stock_log_records):
        """将数据迁移到v3_goods_stock_traceable_code_log表"""
        if not stock_log_records:
            print("[INFO] 没有需要迁移的数据")
            return 0

        migrated_count = 0
        processed_patient_order_ids_in_batch = set()

        for record in stock_log_records:
            try:
                patient_order_id = record.get('patient_order_id')

                # 提取追溯码列表
                traceable_codes = self.extract_traceable_codes_from_goods(record.get('goods'))

                if not traceable_codes:
                    print(f"[WARNING] 记录 {record.get('id')} 没有有效的追溯码数据")
                    continue

                # 为每个追溯码创建一条记录
                record_migrated = False
                for traceable_code in traceable_codes:
                    if not traceable_code or not isinstance(traceable_code, dict):
                        continue

                    # 构建插入数据
                    insert_data = self.build_v3_record(record, traceable_code)

                    if insert_data:
                        # 检查是否已存在相同记录
                        if not self.check_v3_record_exists(insert_data):
                            self.insert_v3_record(insert_data)
                            migrated_count += 1
                            record_migrated = True
                        else:
                            print(f"[DEBUG] 记录已存在，跳过: patient_order_id={patient_order_id}")

                # 如果该patient_order_id的记录有成功迁移的，标记为已处理
                if record_migrated and patient_order_id:
                    processed_patient_order_ids_in_batch.add(patient_order_id)

            except Exception as e:
                print(f"[ERROR] 迁移记录 {record.get('id')} 失败: {str(e)}")
                continue

        # 将本批次成功处理的patient_order_id添加到已处理集合中
        self.processed_patient_order_ids.update(processed_patient_order_ids_in_batch)

        print(f"[INFO] 成功迁移 {migrated_count} 条记录到 v3_goods_stock_traceable_code_log")
        print(f"[INFO] 本批次新增 {len(processed_patient_order_ids_in_batch)} 个已处理的 patient_order_id")
        return migrated_count

    def build_v3_record(self, stock_log_record, traceable_code):
        """构建v3表的记录数据"""
        try:
            # 根据v3_goods_stock_traceable_code_log表结构构建记录
            # 生成唯一ID (这里使用时间戳+随机数，实际可能需要使用雪花算法等)
            record_id = int(time.time() * 1000000) + random.randint(1000, 9999)

            # 解析追溯码数据
            traceable_code_no = traceable_code.get('code', '') or traceable_code.get('no', '')
            piece_count = float(traceable_code.get('pieceCount', 0))
            package_count = float(traceable_code.get('packageCount', 0))

            # 从stock_log_record中获取单位信息（如果有的话）
            goods_data = {}
            if stock_log_record.get('goods'):
                try:
                    goods_data = json.loads(stock_log_record.get('goods'))
                except:
                    pass

            piece_unit = goods_data.get('pieceUnit', '') or goods_data.get('unit', '')
            package_unit = goods_data.get('packageUnit', '') or goods_data.get('packUnit', '')

            # 映射action字段 - 需要将字符串action转换为对应的数字
            action_mapping = {
                '采购入库': 1,
                '采购退货': 2,
                '调拨入库': 3,
                '调拨出库': 4,
                '盘点入库': 5,
                '盘点出库': 6,
                '销售出库': 7,
                '销售退货': 8,
                '领用入库': 9,
                '科室出库': 10,
                '修正入库': 11,
                '修正出库': 12,
                '报损出库': 13,
                '其他入库': 14,
                '其他出库': 15
            }

            action_str = stock_log_record.get('action', '')
            action_code = action_mapping.get(action_str, 0)

            v3_record = {
                'id': record_id,
                'chain_id': stock_log_record.get('chain_id'),
                'clinic_id': stock_log_record.get('organ_id'),  # organ_id映射到clinic_id
                'goods_id': stock_log_record.get('goods_id'),
                'pharmacy_type': 0,  # 默认值
                'pharmacy_no': stock_log_record.get('pharmacy_no', 0),
                'no': traceable_code_no,  # 追溯码
                'no_type': 0,  # 默认为普通码
                'patient_order_id': stock_log_record.get('patient_order_id'),
                'midstream_sheet_id': None,  # 暂时为空，可能需要根据业务逻辑填充
                'midstream_form_item_id': None,
                'downstream_sheet_id': stock_log_record.get('order_id'),  # 使用order_id作为下游单据id
                'downstream_form_item_id': stock_log_record.get('order_detail_id'),
                'action': action_code,
                'change_piece_count': piece_count,
                'change_package_count': package_count,
                'piece_unit': piece_unit,
                'package_unit': package_unit,
                'after_total_piece_count': None,  # 这些字段可能需要额外计算
                'before_total_piece_count': None,
                'is_deleted': 0,
                'created_by': stock_log_record.get('created_user_id', ''),
                'created': stock_log_record.get('created_date'),
                'last_modified_by': stock_log_record.get('created_user_id', ''),
                'last_modified': stock_log_record.get('created_date')
            }

            return v3_record

        except Exception as e:
            print(f"[ERROR] 构建v3记录失败: {str(e)}")
            return None

    def check_v3_record_exists(self, v3_record):
        """检查v3表中是否已存在相同记录"""
        try:
            # 使用更精确的条件检查重复记录
            sql = """
                SELECT COUNT(*) as count
                FROM v3_goods_stock_traceable_code_log
                WHERE chain_id = '{chain_id}'
                  AND clinic_id = '{clinic_id}'
                  AND goods_id = '{goods_id}'
                  AND pharmacy_no = {pharmacy_no}
                  AND no = '{no}'
                  AND patient_order_id = '{patient_order_id}'
                  AND action = {action}
            """.format(
                chain_id=v3_record['chain_id'],
                clinic_id=v3_record['clinic_id'],
                goods_id=v3_record['goods_id'],
                pharmacy_no=v3_record['pharmacy_no'],
                no=v3_record['no'].replace("'", "\\'"),  # 转义单引号
                patient_order_id=v3_record['patient_order_id'],
                action=v3_record['action']
            )

            result = self.goods_db_client.fetchone(sql)
            return result and result.get('count', 0) > 0

        except Exception as e:
            print(f"[ERROR] 检查记录是否存在失败: {str(e)}")
            return False

    def insert_v3_record(self, v3_record):
        """插入记录到v3表"""
        try:
            # 构建插入SQL，按照v3表的字段顺序
            sql = """
                INSERT INTO v3_goods_stock_traceable_code_log (
                    id, chain_id, clinic_id, goods_id, pharmacy_type, pharmacy_no,
                    no, no_type, patient_order_id, midstream_sheet_id, midstream_form_item_id,
                    downstream_sheet_id, downstream_form_item_id, action, change_piece_count,
                    change_package_count, piece_unit, package_unit, after_total_piece_count,
                    before_total_piece_count, is_deleted, created_by, created, last_modified_by, last_modified
                ) VALUES (
                    {id}, '{chain_id}', '{clinic_id}', '{goods_id}', {pharmacy_type}, {pharmacy_no},
                    '{no}', {no_type}, {patient_order_id}, {midstream_sheet_id}, {midstream_form_item_id},
                    {downstream_sheet_id}, {downstream_form_item_id}, {action}, {change_piece_count},
                    {change_package_count}, '{piece_unit}', '{package_unit}', {after_total_piece_count},
                    {before_total_piece_count}, {is_deleted}, '{created_by}', '{created}', '{last_modified_by}', '{last_modified}'
                )
            """.format(
                id=v3_record['id'],
                chain_id=v3_record['chain_id'],
                clinic_id=v3_record['clinic_id'],
                goods_id=v3_record['goods_id'],
                pharmacy_type=v3_record['pharmacy_type'],
                pharmacy_no=v3_record['pharmacy_no'],
                no=v3_record['no'].replace("'", "\\'"),  # 转义单引号
                no_type=v3_record['no_type'],
                patient_order_id=f"'{v3_record['patient_order_id']}'" if v3_record['patient_order_id'] else 'NULL',
                midstream_sheet_id='NULL' if v3_record['midstream_sheet_id'] is None else f"'{v3_record['midstream_sheet_id']}'",
                midstream_form_item_id='NULL' if v3_record['midstream_form_item_id'] is None else f"'{v3_record['midstream_form_item_id']}'",
                downstream_sheet_id='NULL' if v3_record['downstream_sheet_id'] is None else f"'{v3_record['downstream_sheet_id']}'",
                downstream_form_item_id='NULL' if v3_record['downstream_form_item_id'] is None else f"'{v3_record['downstream_form_item_id']}'",
                action=v3_record['action'],
                change_piece_count=v3_record['change_piece_count'],
                change_package_count=v3_record['change_package_count'],
                piece_unit=v3_record['piece_unit'],
                package_unit=v3_record['package_unit'],
                after_total_piece_count='NULL' if v3_record['after_total_piece_count'] is None else v3_record['after_total_piece_count'],
                before_total_piece_count='NULL' if v3_record['before_total_piece_count'] is None else v3_record['before_total_piece_count'],
                is_deleted=v3_record['is_deleted'],
                created_by=v3_record['created_by'],
                created=v3_record['created'],
                last_modified_by=v3_record['last_modified_by'],
                last_modified=v3_record['last_modified']
            )

            self.goods_db_client.execute(sql)

        except Exception as e:
            print(f"[ERROR] 插入v3记录失败: {str(e)}")
            print(f"[DEBUG] 失败的记录: {v3_record}")
            raise

    def migrate_by_date_range(self, start_date, end_date):
        """按日期范围进行迁移"""
        print(f"[INFO] 开始按日期范围迁移: {start_date} 到 {end_date}")

        current_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

        total_migrated = 0

        while current_date <= end_date_obj:
            date_str = current_date.strftime('%Y-%m-%d')
            print(f"\n[INFO] 处理日期: {date_str}")

            try:
                # 1. 获取该日期的patient_order_id列表
                patient_order_ids = self.get_patient_order_ids_by_date(date_str)

                if not patient_order_ids:
                    print(f"[INFO] 日期 {date_str} 没有需要迁移的数据")
                    current_date += timedelta(days=1)
                    continue

                # 2. 查询对应的进销存日志
                stock_log_records = self.get_stock_log_with_traceable_codes(patient_order_ids)

                # 3. 迁移到v3表
                migrated_count = self.migrate_to_v3_table(stock_log_records)
                total_migrated += migrated_count

                print(f"[INFO] 日期 {date_str} 迁移完成，迁移 {migrated_count} 条记录")

                # 添加短暂延迟，避免对数据库造成过大压力
                time.sleep(0.1)

            except Exception as e:
                print(f"[ERROR] 处理日期 {date_str} 失败: {str(e)}")

            current_date += timedelta(days=1)

        print(f"\n[INFO] 迁移完成！总共迁移 {total_migrated} 条记录")
        return total_migrated

    def run(self, start_date=None, end_date=None):
        """执行迁移任务"""
        print(f"[INFO] 开始执行追溯码数据迁移任务")
        print(f"[INFO] 连锁ID: {self.chain_id}, 区域: {self.region_name}, 环境: {self.env}")

        try:
            # 1. 检查是否开启了码上放心功能
            if not self.check_traceable_code_enabled():
                print("[WARNING] 连锁未开启码上放心功能，跳过迁移")
                return

            # 2. 加载已处理的patient_order_id列表
            print("[INFO] 加载已处理的 patient_order_id 列表...")
            self.load_processed_patient_order_ids()

            # 3. 设置默认日期范围（如果未指定）
            if not start_date:
                # 默认从30天前开始
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

            if not end_date:
                # 默认到昨天结束
                end_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

            print(f"[INFO] 迁移日期范围: {start_date} 到 {end_date}")

            # 4. 执行迁移
            total_migrated = self.migrate_by_date_range(start_date, end_date)

            print(f"[SUCCESS] 迁移任务完成！总共迁移 {total_migrated} 条记录")
            print(f"[INFO] 最终处理了 {len(self.processed_patient_order_ids)} 个不重复的 patient_order_id")

        except Exception as e:
            print(f"[ERROR] 迁移任务执行失败: {str(e)}")
            raise


def main():
    parser = argparse.ArgumentParser(description='追溯码采集日志数据迁移工具')
    parser.add_argument('--chain-id', required=True, help='连锁ID')
    parser.add_argument('--region-name', required=True, help='分区名称（如：ShangHai, HangZhou）')
    parser.add_argument('--env', default='prod', help='环境（prod/pre/gray/dev/test）')

    args = parser.parse_args()

    if not args.chain_id or not args.region_name:
        parser.print_help()
        sys.exit(-1)

    try:
        # 创建迁移器实例
        migrator = TraceCodeMigrator(args.region_name, args.chain_id, args.env)

        # 执行实际迁移
        migrator.run(args.start_date, args.end_date)

    except Exception as e:
        print(f"[ERROR] 程序执行失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
