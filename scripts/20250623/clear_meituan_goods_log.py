# -*- coding: utf-8 -*-
"""
@name: flush_goods_supplier_name_py.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-07-24 18:25:27
"""
import argparse
import json
import sys
import requests
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import os
from multizone.db import DBClient
from pypinyin import pinyin, lazy_pinyin, Style
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))


class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', 'prod', True)

    def clear_mt_goods_log(self):
        #  开通美团的就几家店，直接写死chainId
        open_mt_chain_ids = ["ffffffff0000000034f837879f90c001",
        "ffffffff0000000034e3ae583df78000",
         "ffffffff0000000034e6c66182a24000",
         "ffffffff0000000034fc37f545ca4001",
         "ffffffff0000000034edb7fb6b32c000",
         "ffffffff0000000034edb7eeeb300000",
         "ffffffff0000000034e5995f40c78000"]

        if self.chain_id not in open_mt_chain_ids:
            return

        update_sql = """
            update abc_cis_goods_log.v2_goods_stock_log
            set origin_flat_price = null
            where chain_id = '{chain_id}'
              and created_date > '2025-04-06'
              and scene = 25
        """.format(chain_id=self.chain_id)
        # print(update_sql)
        self.goods_db_client.execute(update_sql)

    def run(self):
        self.clear_mt_goods_log()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()