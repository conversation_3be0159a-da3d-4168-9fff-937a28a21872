# -*- coding: utf-8 -*-
"""
@name: flush_pharmacy_config.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-04-15 14:16:33
"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import requests

default_id = '00000000000000000000000000000000'
env = 'prod'


def flushRule(abcRegion, chain_id):
    goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', env, True)
    basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    organs = basic_db_client.fetchall(
        ''' select id as id,his_type as hisType from organ where parent_id = '{chainId}' and his_type = 10 and status = 1 and node_type = 1'''.format(chainId=chain_id))
    # organs = basic_db_client.fetchall(
    #     ''' select id as id,his_type as hisType from organ where his_type = 10 and status = 1 and node_type = 1 '''.format(chainId=chain_id))
    if organs is None or len(organs) == 0:
        return
    for organ in organs:
        hisType = organ['hisType']
        if hisType != 10:
           return
        chainId = organ['id']
        print(chainId)
        goods_db_client.execute(
            """update v2_goods_pharmacy_rule as r inner join  v2_goods_clinic_config as c on r.clinic_id = c.clinic_id and c.his_type = 10 and  r.inner_flag = 1
    SET r.goods_type = JSON_SET(
        r.goods_type,
        '$.typeIdList[0].typeId',
        93
    )
    where  JSON_CONTAINS(
        r.goods_type->'$.typeIdList',
        JSON_OBJECT('typeId', 15),
        '$'
    ) and c.chain_id='{chainId}';""".format(chainId=chainId))

        goods_db_client.execute(
            """
            update v2_goods_chain_config as c set goods_purchase_cycle_days = json_array_insert(
                                                        goods_purchase_cycle_days,
                                                              '$[3]',
                                                              JSON_OBJECT('typeId', 93, 'name', '非配方饮片', 'days', 30))
    where  c.his_type = 10 and not  JSON_CONTAINS(
            c.goods_purchase_cycle_days,
            JSON_OBJECT('typeId', 93),
            '$'
        )
     and c.chain_id='{chainId}';""".format(chainId=chainId ))

        goods_db_client.execute(
            """
            update v2_goods_clinic_config as c set goods_purchase_cycle_days = json_array_insert(
                                                        goods_purchase_cycle_days,
                                                              '$[3]',
                                                              JSON_OBJECT('typeId', 93, 'name', '非配方饮片', 'days', 30))
    where  c.his_type = 10 and not JSON_CONTAINS(
            c.goods_purchase_cycle_days,
            JSON_OBJECT('typeId', 93),
            '$'
        )
     and c.chain_id='{chainId}';""".format(chainId=chainId))

        requests.put("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}""".format(chainId=chainId,rpcHost=regionRpcHost(abcRegion, env=env)))


def run(abcRegion, chain_id):
    flushRule(abcRegion, chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
