#! /usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Prometheus业务监控报表
获取最近2天每天9点到11点的process_cpu_usage平均值
输出格式：服务名，日期，cpu平均值
支持邮件发送功能
"""

import argparse
import logging
import os
import sys
import tempfile
from datetime import datetime, timedelta

import requests
import yagmail

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PrometheusMonitor:
    def __init__(self, prometheus_url, token):
        self.prometheus_url = prometheus_url.rstrip('/')
        self.token = token

    def query_prometheus(self, query, start_time, end_time, step='60s'):
        """
        查询Prometheus数据
        """
        url = f"{self.prometheus_url}/api/v1/query_range"
        params = {
            'query': query,
            'start': start_time,
            'end': end_time,
            'step': step
        }

        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': self.token
        }

        try:
            response = requests.get(url, params=params, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"查询Prometheus失败: {e}")
            return None

    def get_cpu_usage_report(self, days=2):
        """
        获取最近指定天数每天9-11点的CPU使用率平均值、最大值和最小值
        按服务名和日期维度聚合数据
        """
        # 用于存储所有原始数据的字典，key为(service_name, date)
        raw_data = {}

        # 计算日期范围
        today = datetime.now().date()

        for i in range(days):
            target_date = today - timedelta(days=i+1)

            # 设置时间范围：9:00-11:00
            start_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=9))
            end_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=11))

            # 转换为Unix时间戳
            start_timestamp = int(start_datetime.timestamp())
            end_timestamp = int(end_datetime.timestamp())

            logger.info(f"查询日期: {target_date}, 时间范围: {start_datetime} - {end_datetime}")

            # 查询CPU使用率数据
            query = 'process_cpu_usage'
            data = self.query_prometheus(query, start_timestamp, end_timestamp, '60s')

            if not data or data.get('status') != 'success':
                logger.warning(f"查询 {target_date} 数据失败")
                continue

            # 处理查询结果，收集所有原始数据
            for result in data['data']['result']:
                metric = result['metric']
                values = result['values']

                # 获取服务名
                service_name = metric.get('application') or metric.get('job', 'unknown')
                date_str = target_date.strftime('%Y-%m-%d')

                if not values:
                    logger.warning(f"服务 {service_name} 在 {target_date} 无数据")
                    continue

                # 提取有效的CPU值
                cpu_values = [float(value[1]) for value in values if value[1] != 'NaN']

                if cpu_values:
                    # 使用(服务名, 日期)作为key来聚合数据
                    key = (service_name, date_str)
                    if key not in raw_data:
                        raw_data[key] = []
                    raw_data[key].extend(cpu_values)

        # 对聚合后的数据进行统计计算
        results = []
        for (service_name, date_str), all_cpu_values in raw_data.items():
            if all_cpu_values:
                avg_cpu = sum(all_cpu_values) / len(all_cpu_values)
                max_cpu = max(all_cpu_values)
                min_cpu = min(all_cpu_values)

                results.append({
                    'service_name': service_name,
                    'date': date_str,
                    'avg_cpu_usage': round(avg_cpu, 6),
                    'max_cpu_usage': round(max_cpu, 6),
                    'min_cpu_usage': round(min_cpu, 6),
                    'data_points': len(all_cpu_values)  # 添加数据点数量用于调试
                })
                logger.info(f"服务: {service_name}, 日期: {date_str}, 数据点: {len(all_cpu_values)}, CPU平均值: {avg_cpu:.6f}, 最大值: {max_cpu:.6f}, 最小值: {min_cpu:.6f}")
            else:
                logger.warning(f"服务 {service_name} 在 {date_str} 无有效CPU数据")

        return results

    def print_report(self, results):
        """
        打印报表结果
        """
        if not results:
            print("\n❌ 没有找到任何数据")
            return

        print("\n" + "=" * 80)
        print("📊 Prometheus CPU使用率监控报表")
        print("=" * 80)
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 计算最大服务名长度以动态调整列宽
        max_service_name_len = max(len(result['service_name']) for result in results)
        service_col_width = max(20, min(max_service_name_len + 2, 50))

        # 打印表头
        header = f"{'服务名':<{service_col_width}} {'日期':<12} {'CPU平均值':<12} {'CPU最大值':<12} {'CPU最小值':<12} {'数据点':<8}"
        print(header)
        print("-" * len(header))

        # 按服务名和日期排序
        sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))

        # 按服务分组显示
        current_service = None
        for result in sorted_results:
            # 如果是新的服务，添加分隔线
            if current_service != result['service_name']:
                if current_service is not None:
                    print("-" * len(header))
                current_service = result['service_name']

            # 格式化CPU值显示
            avg_cpu_str = f"{result['avg_cpu_usage']:.4f}"
            max_cpu_str = f"{result['max_cpu_usage']:.4f}"
            min_cpu_str = f"{result['min_cpu_usage']:.4f}"
            data_points = result.get('data_points', 0)

            # 添加颜色标识（使用emoji）
            if result['avg_cpu_usage'] > 0.8:
                status_icon = "🔴"  # 高CPU使用率
            elif result['avg_cpu_usage'] > 0.5:
                status_icon = "🟡"  # 中等CPU使用率
            else:
                status_icon = "🟢"  # 低CPU使用率

            service_display = f"{status_icon} {result['service_name']}"

            print(f"{service_display:<{service_col_width+2}} {result['date']:<12} {avg_cpu_str:<12} {max_cpu_str:<12} {min_cpu_str:<12} {data_points:<8}")

        print("=" * len(header))
        print(f"📈 总计: {len(results)} 条记录")
        print(f"🔴 高CPU(>0.8)  🟡 中等CPU(>0.5)  🟢 低CPU(≤0.5)")
        print("=" * 80)

    def export_to_csv(self, results, filename=None):
        """
        导出结果到CSV文件
        """
        if not filename:
            filename = f"prometheus_cpu_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        try:
            import csv
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['service_name', 'date', 'avg_cpu_usage']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                # 按服务名和日期排序
                sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))
                for result in sorted_results:
                    writer.writerow(result)

            logger.info(f"报表已导出到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return None

    def generate_html_report(self, results):
        """
        生成HTML格式的报表，按日期分组展示
        """
        if not results:
            return "<p>没有找到任何数据</p>"

        # 按服务名分组
        services_data = {}
        for result in results:
            service_name = result['service_name']
            if service_name not in services_data:
                services_data[service_name] = []
            services_data[service_name].append(result)

        # 按服务名排序
        sorted_services = sorted(services_data.keys())

        # 获取所有日期并排序
        all_dates = sorted(set(result['date'] for result in results))

        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Prometheus CPU使用率监控报表</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #333; text-align: center; margin-bottom: 10px; }}
        .report-info {{ text-align: center; color: #666; margin-bottom: 20px; }}
        table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 12px 8px; text-align: center; }}
        th {{ background-color: #4CAF50; color: white; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
        tr:hover {{ background-color: #f5f5f5; }}
        .high {{ background-color: #ffebee; color: #d32f2f; font-weight: bold; }}
        .medium {{ background-color: #fff3e0; color: #f57c00; font-weight: bold; }}
        .low {{ background-color: #e8f5e8; color: #388e3c; }}
        .increase {{ background-color: #ffebee; color: #d32f2f; font-weight: bold; }}
        .decrease {{ background-color: #e8f5e8; color: #388e3c; font-weight: bold; }}
        .neutral {{ color: #757575; }}
        .legend {{ margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; font-size: 12px; color: #666; }}
        .legend-item {{ display: inline-block; margin-right: 20px; margin-bottom: 5px; }}
        .service-name {{ text-align: left; font-weight: bold; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Prometheus CPU使用率监控报表</h1>
        <div class="report-info">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
        <table>
            <tr>
                <th class="service-name">服务名</th>"""

        # 添加日期列
        for date_str in all_dates:
            html_content += f"<th>{date_str}</th>"

        # 添加变化幅度列
        if len(all_dates) >= 2:
            html_content += "<th>平均值变化幅度</th>"

        html_content += "</tr>"

        # 添加数据行
        for service_name in sorted_services:
            html_content += f"<tr><td>{service_name}</td>"

            service_results = {result['date']: result for result in services_data[service_name]}
            first_avg = None
            last_avg = None

            for date_str in all_dates:
                if date_str in service_results:
                    result = service_results[date_str]
                    avg_cpu = result['avg_cpu_usage']
                    max_cpu = result['max_cpu_usage']
                    min_cpu = result['min_cpu_usage']

                    # 设置CSS类
                    css_class = ""
                    if avg_cpu > 0.8:
                        css_class = "high"
                    elif avg_cpu > 0.5:
                        css_class = "medium"
                    else:
                        css_class = "low"

                    # 记录第一个和最后一个日期的平均值，用于计算变化幅度
                    if first_avg is None:
                        first_avg = avg_cpu
                    last_avg = avg_cpu

                    html_content += f"<td class='{css_class}'>[{avg_cpu:.6f}|{max_cpu:.6f}|{min_cpu:.6f}]</td>"
                else:
                    html_content += "<td>-</td>"

            # 添加变化幅度
            if len(all_dates) >= 2 and first_avg is not None and last_avg is not None:
                change = last_avg - first_avg
                change_percent = (change / first_avg * 100) if first_avg != 0 else 0

                change_class = "neutral"
                if change_percent > 10:
                    change_class = "increase"
                elif change_percent < -10:
                    change_class = "decrease"

                html_content += f"<td class='{change_class}'>{change_percent:+.2f}%</td>"
            elif len(all_dates) >= 2:
                html_content += "<td>-</td>"

            html_content += "</tr>"

        html_content += """
            </table>
            <p style="margin-top: 20px; font-size: 12px; color: #666;">
                * CPU使用率颜色说明: <span class="high">高(>0.8)</span>, <span class="medium">中(>0.5)</span>, <span class="low">低(≤0.5)</span><br>
                * 变化幅度颜色说明: <span class="increase">增加(>10%)</span>, <span class="decrease">减少(<-10%)</span>, <span class="neutral">稳定(±10%)</span><br>
                * 每个单元格格式为: [平均值|最大值|最小值]
            </p>
        </body>
        </html>
        """

        return html_content

    def send_email_report(self, results, email_config):
        """
        发送邮件报表
        """
        if not yagmail:
            logger.error("yagmail未安装，无法发送邮件")
            return False

        if not results:
            logger.warning("没有数据，跳过邮件发送")
            return False

        try:
            # 生成HTML报表
            html_content = self.generate_html_report(results)

            # 生成CSV附件
            csv_filename = None
            if email_config.get('attach_csv', True):
                with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
                    csv_filename = f.name
                    import csv
                    fieldnames = ['service_name', 'date', 'avg_cpu_usage']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))
                    for result in sorted_results:
                        writer.writerow(result)

            # 配置邮件
            yag = yagmail.SMTP(
                user=email_config['smtp_user'],
                password=email_config['smtp_password'],
                smtp_ssl=True,
                host=email_config.get('smtp_host', 'smtp.exmail.qq.com'),
                port=email_config.get('smtp_port', 465)
            )

            # 生成邮件主题
            today = datetime.now().strftime('%Y-%m-%d')
            subject = f"📊 Prometheus CPU监控报表 - {today}"

            # 发送邮件
            attachments = [csv_filename] if csv_filename else None

            yag.send(
                to=email_config['to_emails'],
                cc=email_config.get('cc_emails', []),
                subject=subject,
                contents=html_content,
                attachments=attachments
            )

            logger.info(f"邮件发送成功，收件人: {email_config['to_emails']}")

            # 清理临时文件
            if csv_filename and os.path.exists(csv_filename):
                os.unlink(csv_filename)

            return True

        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Prometheus业务监控报表')
    parser.add_argument('--prometheus-url', required=False,
                       default="https://cn-shanghai.arms.aliyuncs.com:9443/api/v1/prometheus/cc9088bc80fa0109f85996cb49ce9ff/1858502702438156/ced5f20a675da434bab4501993f588fa1/cn-shanghai",
                       help='Prometheus服务器地址 (例如: http://localhost:9090)')
    parser.add_argument('--token', required=False,
                       help='Prometheus服务器token',
                       default="eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjIwNjQ0MDIyNjYsImlzcyI6Imh0dHA6Ly9hbGliYWJhY2xvdWQuY29tIiwiaWF0IjoxNzQ5MDQyMjY2LCJqdGkiOiJlZWI0NDM5NC0wZDk1LTQ3YjktYTIwYi1lYmQyZDJiN2RjZjkifQ.cOBqt8p3ifZM5f5Kf0D687tDv9UpNn1eKnPCjPgTvm4")
    parser.add_argument('--days', type=int, default=2,
                       help='查询最近几天的数据 (默认: 2)')
    parser.add_argument('--export-csv', action='store_true',
                       help='导出结果到CSV文件')
    parser.add_argument('--csv-filename',
                       help='指定CSV文件名')

    # 邮件相关参数
    parser.add_argument('--send-email', action='store_true',
                       help='发送邮件报表')
    parser.add_argument('--smtp-user',
                       default='<EMAIL>',
                       help='SMTP用户名 (默认: <EMAIL>)')
    parser.add_argument('--smtp-password',
                       default='3j2x5tSGBwiEKBft',
                       help='SMTP密码')
    parser.add_argument('--smtp-host',
                       default='smtp.exmail.qq.com',
                       help='SMTP服务器 (默认: smtp.exmail.qq.com)')
    parser.add_argument('--smtp-port', type=int,
                       default=465,
                       help='SMTP端口 (默认: 465)')
    parser.add_argument('--to-emails', nargs='+',
                       default=['<EMAIL>'],
                       help='收件人邮箱列表')
    # parser.add_argument('--cc-emails', nargs='+',
    #                    default=['<EMAIL>'],
    #                    help='抄送邮箱列表')
    parser.add_argument('--attach-csv', action='store_true',
                       default=False,
                       help='是否附加CSV文件 (默认: True)')

    args = parser.parse_args()

    # 验证Prometheus URL
    if not args.prometheus_url.startswith(('http://', 'https://')):
        logger.error("Prometheus URL必须以http://或https://开头")
        sys.exit(1)

    try:
        # 创建监控实例
        monitor = PrometheusMonitor(args.prometheus_url, args.token)

        # 获取监控数据
        logger.info(f"开始查询最近{args.days}天的CPU使用率数据...")
        results = monitor.get_cpu_usage_report(args.days)

        # 打印报表
        monitor.print_report(results)

        # 导出CSV（如果需要）
        if args.export_csv and results:
            monitor.export_to_csv(results, args.csv_filename)

        # 发送邮件（如果需要）
        if args.send_email and results:
            email_config = {
                'smtp_user': args.smtp_user,
                'smtp_password': args.smtp_password,
                'smtp_host': args.smtp_host,
                'smtp_port': args.smtp_port,
                'to_emails': args.to_emails,
                # 'cc_emails': args.cc_emails,
                'attach_csv': args.attach_csv
            }

            logger.info("正在发送邮件报表...")
            if monitor.send_email_report(results, email_config):
                logger.info("邮件报表发送成功！")
            else:
                logger.error("邮件报表发送失败！")

    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()