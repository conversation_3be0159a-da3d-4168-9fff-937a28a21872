#! /usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Prometheus业务监控报表
获取最近2天每天9点到11点的process_cpu_usage平均值
输出格式：服务名，日期，cpu平均值
支持邮件发送功能
"""

import argparse
import logging
import os
import sys
import tempfile
from datetime import datetime, timedelta

import requests
import yagmail

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PrometheusMonitor:
    def __init__(self, prometheus_url, token):
        self.prometheus_url = prometheus_url.rstrip('/')
        self.token = token
        
    def query_prometheus(self, query, start_time, end_time, step='60s'):
        """
        查询Prometheus数据
        """
        url = f"{self.prometheus_url}/api/v1/query_range"
        params = {
            'query': query,
            'start': start_time,
            'end': end_time,
            'step': step
        }
        
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': self.token
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"查询Prometheus失败: {e}")
            return None
    
    def get_cpu_usage_report(self, days=2):
        """
        获取最近指定天数每天9-11点的CPU使用率平均值、最大值和最小值
        按服务名和日期维度聚合数据
        """
        # 用于存储所有原始数据的字典，key为(service_name, date)
        raw_data = {}
        
        # 计算日期范围
        today = datetime.now().date()
        
        for i in range(days):
            target_date = today - timedelta(days=i+1)
            
            # 设置时间范围：9:00-11:00
            start_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=9))
            end_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=11))
            
            # 转换为Unix时间戳
            start_timestamp = int(start_datetime.timestamp())
            end_timestamp = int(end_datetime.timestamp())
            
            logger.info(f"查询日期: {target_date}, 时间范围: {start_datetime} - {end_datetime}")
            
            # 查询CPU使用率数据
            query = 'process_cpu_usage'
            data = self.query_prometheus(query, start_timestamp, end_timestamp, '60s')
            
            if not data or data.get('status') != 'success':
                logger.warning(f"查询 {target_date} 数据失败")
                continue
                
            # 处理查询结果，收集所有原始数据
            for result in data['data']['result']:
                metric = result['metric']
                values = result['values']
                
                # 获取服务名
                service_name = metric.get('application') or metric.get('job', 'unknown')
                date_str = target_date.strftime('%Y-%m-%d')
                
                if not values:
                    logger.warning(f"服务 {service_name} 在 {target_date} 无数据")
                    continue
                
                # 提取有效的CPU值
                cpu_values = [float(value[1]) for value in values if value[1] != 'NaN']
                
                if cpu_values:
                    # 使用(服务名, 日期)作为key来聚合数据
                    key = (service_name, date_str)
                    if key not in raw_data:
                        raw_data[key] = []
                    raw_data[key].extend(cpu_values)
        
        # 对聚合后的数据进行统计计算
        results = []
        for (service_name, date_str), all_cpu_values in raw_data.items():
            if all_cpu_values:
                avg_cpu = sum(all_cpu_values) / len(all_cpu_values)
                max_cpu = max(all_cpu_values)
                min_cpu = min(all_cpu_values)
                
                results.append({
                    'service_name': service_name,
                    'date': date_str,
                    'avg_cpu_usage': round(avg_cpu, 6),
                    'max_cpu_usage': round(max_cpu, 6),
                    'min_cpu_usage': round(min_cpu, 6),
                    'data_points': len(all_cpu_values)  # 添加数据点数量用于调试
                })
                logger.info(f"服务: {service_name}, 日期: {date_str}, 数据点: {len(all_cpu_values)}, CPU平均值: {avg_cpu:.6f}, 最大值: {max_cpu:.6f}, 最小值: {min_cpu:.6f}")
            else:
                logger.warning(f"服务 {service_name} 在 {date_str} 无有效CPU数据")
        
        return results
    
    def print_report(self, results):
        """
        打印报表结果
        """
        if not results:
            print("没有找到任何数据")
            return
            
        print("\n=== Prometheus CPU使用率监控报表 ===")
        print(f"{'服务名':<40} {'日期':<12} {'CPU平均值':<15} {'CPU最大值':<15} {'CPU最小值':<15}")
        print("-" * 100)
        
        # 按服务名和日期排序
        sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))
        
        for result in sorted_results:
            print(f"{result['service_name']:<40} {result['date']:<12} {result['avg_cpu_usage']:<15.6f} {result['max_cpu_usage']:<15.6f} {result['min_cpu_usage']:<15.6f}")
        
        print(f"\n总计: {len(results)} 条记录")
    
    def export_to_csv(self, results, filename=None):
        """
        导出结果到CSV文件
        """
        if not filename:
            filename = f"prometheus_cpu_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        try:
            import csv
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['service_name', 'date', 'avg_cpu_usage']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                # 按服务名和日期排序
                sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))
                for result in sorted_results:
                    writer.writerow(result)
            
            logger.info(f"报表已导出到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return None
    
    def generate_html_report(self, results):
        """
        生成HTML格式的报表
        """
        if not results:
            return "<p>没有找到任何数据</p>"
        
        # 按服务名和日期排序
        sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))
        
        # 使用内联样式避免CSS解析问题，去除多余空白
        html_content = f"""<!DOCTYPE html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Prometheus CPU使用率监控报表</title></head><body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;"><div style="background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 0;"><h1 style="color: #333; text-align: center; margin: 0 0 30px 0;">🔍 Prometheus CPU使用率监控报表</h1><div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #2196F3;"><strong>📊 报表摘要:</strong><br>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>监控指标: process_cpu_usage<br>时间范围: 每天 09:00-11:00<br>服务总数: {len(set(r['service_name'] for r in results))}<br>记录总数: {len(results)}</div><table style="width: 100%; border-collapse: collapse; margin-top: 20px;"><thead><tr><th style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f8f9fa; font-weight: bold; color: #333;">🏷️ 服务名</th><th style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f8f9fa; font-weight: bold; color: #333;">📅 日期</th><th style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f8f9fa; font-weight: bold; color: #333;">💻 CPU平均值</th><th style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f8f9fa; font-weight: bold; color: #333;">⬆️ CPU最大值</th><th style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f8f9fa; font-weight: bold; color: #333;">⬇️ CPU最小值</th><th style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f8f9fa; font-weight: bold; color: #333;">📊 数据点</th><th style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f8f9fa; font-weight: bold; color: #333;">📈 状态</th></tr></thead><tbody>"""
        
        for result in sorted_results:
            avg_cpu = result['avg_cpu_usage']
            max_cpu = result['max_cpu_usage']
            min_cpu = result['min_cpu_usage']
            data_points = result.get('data_points', 0)
            
            # 根据CPU平均使用率设置颜色和状态
            if avg_cpu > 0.8:
                color_style = 'color: #d32f2f; font-weight: bold;'
                status = '🔴 高'
            elif avg_cpu > 0.5:
                color_style = 'color: #f57c00;'
                status = '🟡 中'
            else:
                color_style = 'color: #388e3c;'
                status = '🟢 低'
            
            html_content += f"""<tr><td style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd;">{result['service_name']}</td><td style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd;">{result['date']}</td><td style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd; {color_style}">{avg_cpu:.6f}</td><td style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd;">{max_cpu:.6f}</td><td style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd;">{min_cpu:.6f}</td><td style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd;">{data_points}</td><td style="padding: 12px; text-align: left; border-bottom: 1px solid #ddd;">{status}</td></tr>"""
        
        html_content += f"""</tbody></table><div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;"><p style="margin: 5px 0;">📧 此报表由Prometheus监控系统自动生成</p><p style="margin: 5px 0;">如有疑问，请联系运维团队</p></div></div></body></html>"""
        
        return html_content
    
    def send_email_report(self, results, email_config):
        """
        发送邮件报表
        """
        if not yagmail:
            logger.error("yagmail未安装，无法发送邮件")
            return False
        
        if not results:
            logger.warning("没有数据，跳过邮件发送")
            return False
        
        try:
            # 生成HTML报表
            html_content = self.generate_html_report(results)
            
            # 生成CSV附件
            csv_filename = None
            if email_config.get('attach_csv', True):
                with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
                    csv_filename = f.name
                    import csv
                    fieldnames = ['service_name', 'date', 'avg_cpu_usage']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))
                    for result in sorted_results:
                        writer.writerow(result)
            
            # 配置邮件
            yag = yagmail.SMTP(
                user=email_config['smtp_user'],
                password=email_config['smtp_password'],
                smtp_ssl=True,
                host=email_config.get('smtp_host', 'smtp.exmail.qq.com'),
                port=email_config.get('smtp_port', 465)
            )
            
            # 生成邮件主题
            today = datetime.now().strftime('%Y-%m-%d')
            subject = f"📊 Prometheus CPU监控报表 - {today}"
            
            # 发送邮件
            attachments = [csv_filename] if csv_filename else None
            
            yag.send(
                to=email_config['to_emails'],
                cc=email_config.get('cc_emails', []),
                subject=subject,
                contents=html_content,
                attachments=attachments
            )
            
            logger.info(f"邮件发送成功，收件人: {email_config['to_emails']}")
            
            # 清理临时文件
            if csv_filename and os.path.exists(csv_filename):
                os.unlink(csv_filename)
            
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Prometheus业务监控报表')
    parser.add_argument('--prometheus-url', required=False,
                       default="https://cn-shanghai.arms.aliyuncs.com:9443/api/v1/prometheus/cc9088bc80fa0109f85996cb49ce9ff/1858502702438156/ced5f20a675da434bab4501993f588fa1/cn-shanghai",
                       help='Prometheus服务器地址 (例如: http://localhost:9090)')
    parser.add_argument('--token', required=False,
                       help='Prometheus服务器token', 
                       default="eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjIwNjQ0MDIyNjYsImlzcyI6Imh0dHA6Ly9hbGliYWJhY2xvdWQuY29tIiwiaWF0IjoxNzQ5MDQyMjY2LCJqdGkiOiJlZWI0NDM5NC0wZDk1LTQ3YjktYTIwYi1lYmQyZDJiN2RjZjkifQ.cOBqt8p3ifZM5f5Kf0D687tDv9UpNn1eKnPCjPgTvm4")
    parser.add_argument('--days', type=int, default=2, 
                       help='查询最近几天的数据 (默认: 2)')
    parser.add_argument('--export-csv', action='store_true',
                       help='导出结果到CSV文件')
    parser.add_argument('--csv-filename',
                       help='指定CSV文件名')
    
    # 邮件相关参数
    parser.add_argument('--send-email', action='store_true',
                       help='发送邮件报表')
    parser.add_argument('--smtp-user', 
                       default='<EMAIL>',
                       help='SMTP用户名 (默认: <EMAIL>)')
    parser.add_argument('--smtp-password', 
                       default='3j2x5tSGBwiEKBft',
                       help='SMTP密码')
    parser.add_argument('--smtp-host', 
                       default='smtp.exmail.qq.com',
                       help='SMTP服务器 (默认: smtp.exmail.qq.com)')
    parser.add_argument('--smtp-port', type=int,
                       default=465,
                       help='SMTP端口 (默认: 465)')
    parser.add_argument('--to-emails', nargs='+',
                       default=['<EMAIL>'],
                       help='收件人邮箱列表')
    # parser.add_argument('--cc-emails', nargs='+',
    #                    default=['<EMAIL>'],
    #                    help='抄送邮箱列表')
    parser.add_argument('--attach-csv', action='store_true',
                       default=False,
                       help='是否附加CSV文件 (默认: True)')
    
    args = parser.parse_args()
    
    # 验证Prometheus URL
    if not args.prometheus_url.startswith(('http://', 'https://')):
        logger.error("Prometheus URL必须以http://或https://开头")
        sys.exit(1)
    
    try:
        # 创建监控实例
        monitor = PrometheusMonitor(args.prometheus_url, args.token)
        
        # 获取监控数据
        logger.info(f"开始查询最近{args.days}天的CPU使用率数据...")
        results = monitor.get_cpu_usage_report(args.days)
        
        # 打印报表
        monitor.print_report(results)
        
        # 导出CSV（如果需要）
        if args.export_csv and results:
            monitor.export_to_csv(results, args.csv_filename)
        
        # 发送邮件（如果需要）
        if args.send_email and results:
            email_config = {
                'smtp_user': args.smtp_user,
                'smtp_password': args.smtp_password,
                'smtp_host': args.smtp_host,
                'smtp_port': args.smtp_port,
                'to_emails': args.to_emails,
                # 'cc_emails': args.cc_emails,
                'attach_csv': args.attach_csv
            }
            
            logger.info("正在发送邮件报表...")
            if monitor.send_email_report(results, email_config):
                logger.info("邮件报表发送成功！")
            else:
                logger.error("邮件报表发送失败！")
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()