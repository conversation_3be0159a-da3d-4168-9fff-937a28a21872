#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import sys
import argparse
import time

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from itertools import groupby
from datetime import datetime, timedelta
from idwork import IdWork

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import requests


default_id = '00000000000000000000000000000000'




class UpdateData:
    chain_id = None
    basic_db_client = None
    id_work = None
    type = None
    businessType = None

    def __init__(self, region_name, env):

        self.region_name = region_name
        self.env = env
        self.charge_db_client = DBClient(self.region_name, 'abc_cis_charge', 'abc_cis_charge', self.env, False)

    def run(self):
        self.flush_medicine_config()

    ###

    def flush_medicine_config(self):
        batch_size = 300  # 每次UPDATE处理的ID数量
        hours_per_window = 1  # 每个时间窗口的小时数
        
        # 定义时间范围
        start_time = datetime.strptime('2025-04-15 00:00:00', '%Y-%m-%d %H:%M:%S')
        end_time = datetime.strptime('2025-04-24 00:00:00', '%Y-%m-%d %H:%M:%S')
        
        current_start = start_time
        
        while current_start < end_time:
            # 计算当前时间窗口的结束时间
            current_end = current_start + timedelta(hours=hours_per_window)
            
            # 确保不超过总结束时间
            if current_end > end_time:
                current_end = end_time
            
            # 格式化为SQL需要的字符串格式
            start_str = current_start.strftime('%Y-%m-%d %H:%M:%S')
            end_str = current_end.strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"Processing time window: {start_str} to {end_str}")
            
            # 查询当前时间窗口内的所有ID
            query = f"""
                SELECT id FROM v2_charge_sheet 
                WHERE is_deleted = 0 
                AND created >= '{start_str}' 
                AND created < '{end_str}'
                ORDER BY created, id
            """
            
            register_info_list = self.charge_db_client.fetchall(query)
            
            if not register_info_list:
                current_start = current_end
                continue
            
            # 分批处理当前时间窗口内的ID
            for i in range(0, len(register_info_list), batch_size):
                batch_ids = [str(record['id']) for record in register_info_list[i:i + batch_size]]
                id_list = "','".join(batch_ids)
                
                updatesql = f"""
                    UPDATE v2_charge_sheet
                    SET created = DATE_ADD(created, INTERVAL 1 SECOND)
                    WHERE id IN ('{id_list}')
                """
                time.sleep(0.3)
                print(updatesql)
                self.charge_db_client.execute(updatesql)
            
            # 移动到下一个时间窗口
            current_start = current_end


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-name', help='分区名字')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    region_name = args.region_name
    env = args.env

    try:
        updateData = UpdateData(region_name, env)
        updateData.run()
    except Exception as e:
        print(f"运行时发生错误: {e}")
        sys.exit(1)





if __name__ == '__main__':
    main()
