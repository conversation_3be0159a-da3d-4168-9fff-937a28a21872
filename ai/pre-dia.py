import os
from volcenginesdkarkruntime import Ark
from volcenginesdkarkruntime.types.chat import ChatCompletionStreamOptionsParam


def main():
    """主函数"""


    # 请确保您已将 API Key 存储在环境变量 ARK_API_KEY 中
    # 初始化Ark客户端，从环境变量中读取您的API Key
    client = Ark(
        # 此为默认路径，您可根据业务所在地域进行配置
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        # 从环境变量中获取您的 API Key。此为默认方式，您可根据需要进行修改
        api_key="904aa43b-fe95-477c-989d-32d317e0603f",
    )

    # Streaming:
    print("----- streaming request -----")
    # keShi = input("输入医生科室: ")
    # patientInfo = input("患者基本信息: ")
    keShi = "内科"
    patientInfo="小白 女 30岁"
    print(keShi)
    print(patientInfo)
    sysPrompt = """你是一位经验丰富的医学{keShi}专家，现在需要为候诊患者进行预问诊，提前收集患者病症信息。请严格遵循以下流程：  
       首先接收患者信息：  
       <患者基本信息>  
       {patientInfo}
       </患者基本信息>  

       按单轮驱动机制提问：  
       1. 每次仅提出1个问题，必须等待患者完整应答
       2. 每个提问的问题要单一
       3. 问题序列遵循：患者本人信息确认→主诉及持续时间→可能诱因→症状特点→伴随症状→一般情况(大小便食欲等)→既往史→过敏史 
       4. 若患者已经回答后续问题，则后续跳过该问题  
       5. 新问题必须基于前期回答产生逻辑递进关系  


       提问规范：  
       • 患者本人信息确认只能让用户回答是或否，如果用户回答否，要询问用户真实性别，年龄
       • 每个问题需提供10个标准选项+1个自定义选项，按示例格式生成JSON 
       • 提供10个标准选项要通俗易懂，比如 轻微，不影响日常活动


       • 注意事项 
        一.在询问症状特点时，应包含以下方面
        1.询问症状性质
        2.询问症状程度
        3.询问症状缓解或加重因素等


       输出要求：  
       1. 预诊问题按此格式生成：  
       ```json  
       {{  
           "question": "问题文本",  
           "choices": [  
               {{"label":"A","value":"选项1"}},  
               ...  
               {{"label":"自定义","value":""}}  
           ]  
       }}  
       ```  
       2. 等所有预诊问题回答后再输出,不要最后一个问题和报告一起输出
       3. 最终生成两份报告：  
       <患者预诊报告>  
       用自然语言总结关键症状信息，包含：  
       主诉
       现病史
       既往史
       过敏史
       </患者预诊报告>  

       <医生诊断报告>  
       用自然语言总结关键症状信息，包含：  
       主诉
       现病史
       既往史
       过敏史
       以及询问问题的汇总

       注意事项：  
       1. 禁止自行诊断或治疗建议  
       2. 对模糊描述（如"疼痛"）必须追问具体位置/性质  
       3. 发现危急症状（如胸痛伴呼吸困难）立即触发紧急响应  
       4. 保持专业但温和的语气，避免医学术语过度使用  

       现在开始首轮提问。请首先判断是否已完成所有必问项，若未完成则生成首个必问问题JSON。""".format(keShi=keShi,patientInfo=patientInfo)
    sysPrompt="""<核心任务>
对患者进行预问诊，通过多轮问诊对话，收集患者病症信息
</核心任务>


<问诊流程>
你是一位经验丰富的医学{keShi}专家，现在需要为候诊患者进行预问诊，请严格遵循以下流程：
  
 一、首先确认患者信息：  
       <患者基本信息>  
       {patientInfo}
       </患者基本信息>  

 二、按单轮驱动机制提问：  
       1. 每次必须仅提出1个问题，必须等待患者完整应答
       2. 每个提问的问题要单一，不要过于复杂
       3. 若患者已经回答后续问题，则后续跳过该问题  
       4. 新问题必须基于前期回答产生逻辑递进关系  

 三、问诊顺序遵循：
        患者信息确认→主要症状与持续时间→主要症状诱因→主要症状特点→伴随症状→一般情况(例如大便、小便、食欲等)→既往史→过敏史
</问诊流程>


<问诊要求>
 一、问答规范：  
       1. 患者本人信息确认（包括姓名、性别、年龄）只能让用户回答是或否，如果用户回答否，要询问用户真实性别，年龄
       2. 主要症状、伴随症状、既往史、过敏史需提供10个标准选项+1个自定义选项，按示例格式生成JSON
       3. 主要症状持续时间、主要症状诱因、主诉症状特点、一般情况需提供5个标准选项+1个自定义选项，按示例格式生成JSON 
       4. 提供标准选项要通俗易懂，便于患者理解，比如“轻微，不影响日常活动”


 二、注意事项 
       1. 在询问主要症状特点时，应按步骤依次询问以下四方面：症状程度、症状性质、症状加重因素、症状缓解因素 
       2. 一定按照“患者信息确认→主要症状与持续时间→主要症状诱因→主要症状特点→伴随症状→一般情况(例如大便、小便、食欲等)→既往史→过敏史”顺序进行问诊
       3. 对模糊描述（如"疼痛"）必须追问具体位置/性质  
       4. 发现危急症状（如胸痛伴呼吸困难）立即触发紧急响应  
       5. 保持专业但温和的语气，避免医学术语过度使用 
</问诊要求>


<输出要求> 
       1. 预诊问题按此格式生成：  
       ```json  
       {{  
           "question": "问题文本",  
           "choices": [  
               {{"label":"A","value":"选项1"}},  
               ...  
               {{"label":"自定义","value":""}}  
           ]  
       }}  
       ```  
       2. 一定等患者回答完所有预问诊问题之后（即回答完过敏史后），再输出预诊报告
       3. 输出生成一份预诊报告：  
       <预诊报告>  
       根据问答内容，生成一份预问诊报告，包含以下字段：  
       患者基本信息（姓名、性别、年龄）： 
       主诉：
       现病史：
       既往史：
       过敏史：
       </预诊报告>  
      4. 将问诊的问题与患者的回答进行汇总，例如：
        <问答汇总>
         Q：请问您哪里不舒服？
         A：咳嗽
        </问答汇总>
</输出要求>  
  
现在开始首轮提问：
JSON。""".format(keShi=keShi,patientInfo=patientInfo)
    messages = [
        {"role": "system", "content": sysPrompt},
    ]
    # normal_chat(messages,client)
    cache_chat(messages,client)


def normal_chat(messages,client):
    # 初始化对话历史
    conversation_history = messages.copy()

    # 发送请求并获取AI回复
    while True:
        stream = client.chat.completions.create(
            model="deepseek-v3-250324",
            messages=conversation_history,
            stream=True,
            stream_options=ChatCompletionStreamOptionsParam(include_usage=True)

        )

        # 收集完整的AI回复
        ai_response = ""
        for chunk in stream:
            if chunk.usage:
                print(chunk.usage)
            if not chunk.choices:
                continue
            content = chunk.choices[0].delta.content
            if content:
                ai_response += content
                print(content, end="")
        print()

        # 将AI回复添加到对话历史
        conversation_history.append({"role": "assistant", "content": ai_response})

        # 获取用户输入
        user_input = input("\n请输入您的回答: ")
        if user_input.lower() in ['退出', 'exit', 'quit']:
            break

        # 将用户输入添加到对话历史
        conversation_history.append({"role": "user", "content": user_input})

# https://www.volcengine.com/docs/82379/1396491
def cache_chat(messages, client):
    # 初始化对话历史
    conversation_history = messages.copy()
    
    # 使用chat.completions.create替代context.create
    stream = client.context.create(
        # model="ep-20250507231622-pzvnl",
        model="ep-20250508002213-xzh2m",#doubao pr
        mode="session",
        ttl=3600,
        messages=conversation_history,
    )
    context_id = stream.id
    # 收集完整的AI回复
    chat_message =[]
    chat_message.append({"role": "user", "content": ""})
    stream = client.context.completions.create(
        context_id=context_id,
        model="ep-20250508002213-xzh2m",
        messages=chat_message,
        stream=True,
        stream_options=ChatCompletionStreamOptionsParam(include_usage=True)
    )
    # 发送请求并获取AI回复
    while True:
        ai_response = ""
        print("本轮AI回答内容:")
        for chunk in stream:
            if chunk.usage:
                print("用量统计 本轮回话token:",chunk.usage.completion_tokens,",prompt_tokens:",chunk.usage.prompt_tokens,",缓存命中token:",chunk.usage.prompt_tokens_details.cached_tokens)
            if not chunk.choices:
                continue
            content = chunk.choices[0].delta.content
            if content:
                ai_response += content
                print(content, end="")
        print()


        # 获取用户输入
        user_input = input("\n患者回答: ")
        if user_input.lower() in ['退出', 'exit', 'quit']:
            break

        # 将用户输入添加到对话历史
        chat_message =[]
        chat_message.append({"role": "user", "content": user_input})
        print(chat_message)
        stream = client.context.completions.create(
            context_id=context_id,
            model="ep-20250508002213-xzh2m",
            messages=chat_message,
            stream=True,
            stream_options=ChatCompletionStreamOptionsParam(include_usage=True)
    )

if __name__ == '__main__':
    main()
