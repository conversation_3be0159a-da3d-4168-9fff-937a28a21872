#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright 2021 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

from multizone.db import DBClient
import argparse
import sys
import os
import ast
import logging
import threading
# ANSI颜色代码
RED = '\033[91m'
GREEN = '\033[92m'
RESET = '\033[0m'

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

GRAY_ENV_MAP = {
    'prod': 0,
    'gray': 1,
    'pre': 2,
}

ENV_TO_GRAY_MAP = {
    0: 'prod',
    1: 'gray',
    2: 'pre'
}


def script_abs_path(path):
    return os.path.join(CURRENT_DIR, 'scripts', path)


class MigrateManagment(object):
    def __init__(self, env, abcRegion):
        self.db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', env, True)

    # 按环境刷数据u
    def migrate_by_env(self, env, chainId, abcRegion):
        abcRegionId = 0
        if abcRegion == 'ShangHai':
            abcRegionId = 1
        elif abcRegion == 'HangZhou':
            abcRegionId = 2
        if chainId == None or len(chainId) == 0:
            if env == 'dev' or env == 'test':
                organs = self.db_client.fetchall('''
                    select a.id from organ as a
                    where a.status = 1 and a.node_type = 1;
                ''')
            elif env == 'prod':
                organs = self.db_client.fetchall('''
                    select a.id from organ as a
                    inner join  v2_clinic_region_organ as rg on a.parent_id = rg.chain_id and rg.region_id = '{abcRegion}'
                    left join v2_clinic_gray_organ as b on a.id = b.chain_id and b.env in (1, 2)
                    where a.status = 1 and a.node_type = 1 and b.id is null;
                '''.format(abcRegion=abcRegionId))
            else:
                organs = self.db_client.fetchall(
                    ''' select a.chain_id as id 
                        from v2_clinic_gray_organ as a  
                            inner join  v2_clinic_region_organ as rg 
                            on a.chain_id = rg.chain_id and rg.region_id = '{abcRegion}'
                            where a.env = '{env}' '''.format(env=GRAY_ENV_MAP[env],
                                                             abcRegion=abcRegionId))
            # 本次要执行的连锁Id列表
            chain_ids = [organ['id'] for organ in organs]
        else:
            chain_ids = [chainId]

        # 找本次要执行的脚本
        scripts = self.db_client.fetchall("""select cast(id as char) as id, 
                                                    name, 
                                                    path, 
                                                    thread_num,
                                                    execute_type 
                                             from v2_data_migrate_script 
                                             where is_enable_in_{0} = 1""".format(env))
        # 执行
        self.migrate_by_chains(chain_ids, scripts, abcRegion, None, env)

    # 纯刷连锁数据,每次指定要刷
    def pure_flushdata_by_env(self, env, chainId, scriptPath, flushChainCount, abcRegion):
        abcRegionId = 0
        if abcRegion == 'ShangHai':
            abcRegionId = 1
        elif abcRegion == 'HangZhou':
            abcRegionId = 2
        if chainId == None or len(chainId) == 0:
            if env == 'prod':
                organs = self.db_client.fetchall('''
                    select a.id 
                    from organ as a
                        inner join  v2_clinic_region_organ as rg on a.parent_id = rg.chain_id and rg.region_id = '{abcRegion}'
                        left join v2_clinic_gray_organ as b on a.id = b.chain_id and b.env in (1, 2)
                    where a.status = 1 
                        and a.node_type = 1 
                        and b.id is null 
                    ;
                '''.format(abcRegion=abcRegionId))
            else:
                organs = self.db_client.fetchall('''
                    select a.chain_id as id 
                    from v2_clinic_gray_organ  as a
                    inner join  v2_clinic_region_organ as rg on a.chain_id = rg.chain_id and rg.region_id = '{abcRegion}'
                    where a.env = '{envStr}' 
                    '''.format(envStr=GRAY_ENV_MAP[env], abcRegion=abcRegionId))
            # 本次要执行的连锁Id列表
            chain_ids = [organ['id'] for organ in organs]
        else:
            chain_ids = [chainId]

        # 找本次要执行的脚本
        # 直接用脚本相对路径名字进行查找要执行的脚本
        scripts = self.db_client.fetchall("""select cast(id as char) as id, 
                                                    name, 
                                                    path, 
                                                    thread_num,
                                                    execute_type 
                                             from v2_data_migrate_script 
                                             where trim(path)=trim('{scriptPath}')""".format(scriptPath=scriptPath))
        # 执行
        self.migrate_by_chains(chain_ids, scripts, abcRegion, flushChainCount, env)

    def migrate_by_chains(self, chain_ids, scripts, abcRegion, flushChainCount, env):
        # 查询当前环境下需要执行的脚本和已执行的记录
        script_ids = ",".join([script['id'] for script in scripts])

        # 脚本的执行情况
        execute_records = self.db_client.fetchall(
            """select cast(script_id as char) as script_id, chain_id from v2_data_migrate_script_organ_execute where script_id in ({})""".format(
                script_ids))

        # chain_ids 范围内 ，脚本Id 已经执行了的情况
        script_id_to_executed_chain_ids = {}
        for recode in execute_records:
            script_id = recode['script_id']
            chain_id = recode['chain_id']
            if script_id in script_id_to_executed_chain_ids:
                script_id_to_executed_chain_ids[script_id].add(chain_id)
            else:
                script_id_to_executed_chain_ids[script_id] = {chain_id}

        # 再来找出本次要执行 连锁ID里面 有多少连锁没执行
        for script in scripts:
            execute_type = script['execute_type']
            thread_num = script['thread_num']
            # 交集 找到这个脚本 还需要执行的连锁ID
            need_execute_chain_ids = list(
                set(chain_ids).difference(script_id_to_executed_chain_ids.get(script['id'], [])))
            if len(need_execute_chain_ids) <= 0:
                continue
            if flushChainCount is not None:
                need_execute_chain_ids = need_execute_chain_ids[0:flushChainCount]

            # 将need_execute_chain_ids 分成5组
            MAX_LEN = len(need_execute_chain_ids) // thread_num + 1
            listRun = [need_execute_chain_ids[i:i + MAX_LEN] for i in range(0, len(need_execute_chain_ids), MAX_LEN)]
            # 存放线程对象
            threads = []
            for chainIdsSubItem in listRun:
                # 过滤掉chainIdsSubItem里面的空值
                chainIdsSubItem = list(filter(lambda x: x != '', chainIdsSubItem))
                if len(chainIdsSubItem) <= 0:
                    logging.info(u'----执行脚本:【{0}】,分组连锁ID为空，直接退出不需要刷 '.format(script['name']))
                    continue

                t = threading.Thread(target=self._thread_run_script_for_chains,
                                     args=(script, chainIdsSubItem, abcRegion, execute_type, env))
                t.start()
                threads.append(t)
            for t in threads:
                t.join()
            logging.info(u'----执行脚本:【{0}】,所有线程已执行完毕,任务结束 '.format(script['name']))

    def _thread_run_script_for_chains(self, script, chain_ids, abcRegion, execute_type, env):
        logging.info(
            u'----执行脚本:【{0}】,启动线程Id:{2},执行类型:(0一个连锁一个连锁的跑，1一批连锁一批的跑):{1},本线程处理连锁ID数量:{3}'.format(
                script['name'], execute_type, threading.current_thread().ident, len(chain_ids)))
        if execute_type == 0:
            for chain_id in chain_ids:
                self._run_script_for_chain(script, chain_id, abcRegion, env)
        elif execute_type == 1:
            self._run_script_for_chains(script, chain_ids, abcRegion, env)

    def _run_script_for_chain(self, script, chain_id, abcRegion, env):
        logging.info(u'运行脚本:【{0}】 连锁ID:{1} 分区Id:{2} 分区名字:{3}'.format(script['name'], chain_id, abcRegion,
                                                                                 abcRegion))
        command = ''' python3 {0} --chain-id {1} --region-name {2} --env {3}'''.format(script_abs_path(script['path']),
                                                                                       chain_id,
                                                                                       abcRegion, env)
        ret = os.system(command)
        if ret == 0:
            dbClient = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', env, True)
            dbClient.execute(
                '''insert into v2_data_migrate_script_organ_execute(chain_id,script_id,status) values('{chainId}',{script_id},1)'''.format(
                    chainId=chain_id, script_id=script['id']))

    def _run_script_for_chains(self, script, chain_ids, abcRegion, env):
        gap = 1000
        size = len(chain_ids) / gap + 1
        for i in range(0, size):
            segments = []
            for j in range(i * 1000, (i + 1) * 1000):
                if j >= len(chain_ids):
                    break
                segments.append(chain_ids[j])
            # 执行size次
            command = ''' python3 {0} --script-id {1} --chain-ids {2} --region-name {3} --env {4}'''.format(
                script_abs_path(script['path']),
                script['id'], " ".join(segments), abcRegion, env)
            ret = os.system(command)
            logging.info(u'run script {0} result: {1}'.format(script['name'], ret))

    # 指定连锁ID啦灰度 chainIdStr = ‘chainid1’,'chainId2’
    def pull_chain_id_to_gray(self, env, chainIdStr,not_pull_province_name):
        print(f"连锁ID解析错误: {chainIdStr}")
        # 处理连锁ID列表
        chain_ids = chainIdStr.split(',') if chainIdStr else []
        # 使用处理后的数据
        print("处理连锁ID:", chain_ids)
        id_condition = ''
        # 构建SQL查询
        if chain_ids:
            # 将每个ID用单引号包围，然后用逗号连接
            id_condition = ','.join([f"'{id}'" for id in chain_ids])

        sql = '''
        insert ignore into   v2_clinic_gray_organ(chain_id, name, env, region_id, his_type, remark)
        select o.id, o.name, 1, r.region_id, o.his_type,null
        from organ as o
                 inner join v2_clinic_region_organ as r on o.id = r.chain_id
        where o.node_type = 1 
          and o.id in (
                {chainIdStr}
            )
        '''.format(chainIdStr=id_condition, env=env)
        if  not_pull_province_name is not None and len(not_pull_province_name) > 0:
            sql = sql + """ and o.address_province_name not like {not_pull_province_name}""".format(
                not_pull_province_name=not_pull_province_name)
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------指定连锁ID拉灰度:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    # 指定连锁名字拉灰度 chainNameStr =  name like '%陈忠福诊所%' or name like '%渝北桦煜堂诊所悦山店%'
    def pull_chain_name_to_gray(self,chainNameStr,not_pull_province_name):
        if chainNameStr is None or len(chainNameStr) == 0:
            return
        # 处理连锁ID列表
        chain_ids = chainNameStr.split(',') if chainNameStr else []
        # 使用处理后的数据
        print("处理连锁ID:", chain_ids)
        id_condition = ''
        # 构建SQL查询
        if chain_ids:
            # 将每个ID用单引号包围，然后用逗号连接
            id_condition = ' or '.join([f" o.name like '%{id}%'" for id in chain_ids])

        sql = '''
        insert ignore into   v2_clinic_gray_organ(chain_id, name, env, region_id, his_type, remark)
        select o.id, o.name, 1, r.region_id, o.his_type,null
        from organ as o
                 inner join v2_clinic_region_organ as r on o.id = r.chain_id
        where o.node_type = 1
          and ( {chainNameStr} ) 
        '''.format(chainNameStr=id_condition)

        if  not_pull_province_name is not None and len(not_pull_province_name) > 0:
            sql = sql + """ and o.address_province_name not like {not_pull_province_name}""".format(
                not_pull_province_name=not_pull_province_name)
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------指定连锁名字拉灰度:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    # 指定数量从正式拉到灰度
    def pull_to_env_with_limit(self, env, abcRegionId, pullCount, activeDate,not_pull_province_name):
        if pullCount is None :
            return
        if  not_pull_province_name is not None and len(not_pull_province_name) > 0:
            sql = '''
                insert ignore into   v2_clinic_gray_organ(chain_id, name, env, region_id, his_type)
                select v2_clinic_active_chain.chain_id, organ.name, 1, null, v2_clinic_region_organ.region_id, organ.his_type
                from v2_clinic_active_chain
                    left join v2_clinic_gray_organ on v2_clinic_active_chain.chain_id = v2_clinic_gray_organ.chain_id
                    inner join v2_clinic_region_organ on v2_clinic_active_chain.chain_id = v2_clinic_region_organ.chain_id
                    inner join organ on v2_clinic_active_chain.chain_id = organ.id
                where v2_clinic_active_chain.last_active_date >= '{activeDate}'
                        and v2_clinic_gray_organ.id is null
                        and v2_clinic_region_organ.region_id = {regionId}
                        and organ.address_province_name not like {not_pull_province_name}
                limit {pullCount} 
            '''.format(activeDate=activeDate, regionId=abcRegionId, pullCount=pullCount, env=env,not_pull_province_name=not_pull_province_name)
        else:
            sql = '''
                insert ignore into   v2_clinic_gray_organ(chain_id, name, env,remark, region_id, his_type)
                select v2_clinic_active_chain.chain_id, organ.name,1, null, v2_clinic_region_organ.region_id, organ.his_type
                from v2_clinic_active_chain
                    left join v2_clinic_gray_organ on v2_clinic_active_chain.chain_id = v2_clinic_gray_organ.chain_id
                    inner join v2_clinic_region_organ on v2_clinic_active_chain.chain_id = v2_clinic_region_organ.chain_id
                    inner join organ on v2_clinic_active_chain.chain_id = organ.id
                where v2_clinic_active_chain.last_active_date >= '{activeDate}'
                        and v2_clinic_gray_organ.id is null
                        and v2_clinic_region_organ.region_id = {regionId}
                limit {pullCount} 
            '''.format(activeDate=activeDate, regionId=abcRegionId, pullCount=pullCount, env=env)
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------拉活跃门店到灰度:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    # 把所有不活跃拉到灰度
    def pull_non_active_to_gray(self, activeDate):
        sql = '''
                insert ignore into   v2_clinic_gray_organ(chain_id, name, env,remark, region_id, his_type)
                select v2_clinic_active_chain.chain_id, organ.name, 1, null, v2_clinic_region_organ.region_id, organ.his_type
                from organ
                         left join v2_clinic_active_chain on v2_clinic_active_chain.chain_id = organ.id
                         left join v2_clinic_gray_organ on organ.id = v2_clinic_gray_organ.chain_id
                         inner join v2_clinic_region_organ on v2_clinic_active_chain.chain_id = organ.id
                where  (v2_clinic_active_chain.last_active_date <= '{activeDate}' or v2_clinic_active_chain.chain_id is null)
                  and v2_clinic_gray_organ.id is null
        '''.format(activeDate=activeDate)
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------不活跃门店({activeDate})拉到灰度:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    def pull_big_chain_to_gray(self):
        sql = '''
                update  v2_clinic_gray_organ 
                set env = 1,last_modified=now() 
                where env = 0  
                        and remark is not null 
                        and remark not like '%测试%'
        '''
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------把大店拉到灰度:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    def push_big_chain_to_prod(self):
        sql = '''
                update  v2_clinic_gray_organ 
                set env = 0,last_modified=now() 
                where env = 1 
                        and remark is not null 
                        and remark not like '%测试%'
        '''
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------把大店踢到正式:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    # 把省的用户拉到灰度
    def pull_province_to_gray(self, proviceName):
        if proviceName is None or len(proviceName) == 0:
            return
        sql = '''
                insert ignore into   v2_clinic_gray_organ(chain_id, name, env,remark, region_id, his_type)
                select organ.id, organ.name, 1, null, v2_clinic_region_organ.region_id, organ.his_type
                from organ
                         left join v2_clinic_gray_organ  on v2_clinic_gray_organ.chain_id = organ.id
                         inner join v2_clinic_region_organ on organ.id = v2_clinic_region_organ.chain_id
                where  v2_clinic_gray_organ.id is null
                    and organ.address_province_name like '%{proviceName}%';
        '''.format(proviceName=proviceName)
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------拉{proviceName}到灰度:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    def pull_all_normal_to_gray(self):
        sql = '''
                insert ignore into  v2_clinic_gray_organ(chain_id, name, env,remark, region_id, his_type)
                select organ.id, organ.name, 1, null, v2_clinic_region_organ.region_id, organ.his_type
                from organ
                         left join v2_clinic_gray_organ  on v2_clinic_gray_organ.chain_id = organ.id
                         inner join v2_clinic_region_organ on organ.id = v2_clinic_region_organ.chain_id
                where  v2_clinic_gray_organ.id is null
        '''
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------拉全部普通门店到灰度:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")
    # 把欧个市的用户拉到灰度
    def pull_city_to_gray(self, cityName):
        if cityName is None or len(cityName) == 0:
            return
        sql = '''
                insert ignore into   v2_clinic_gray_organ(chain_id, name, env,remark,  region_id, his_type)
                select organ.id, organ.name, 1, null, v2_clinic_region_organ.region_id, organ.his_type
                from organ
                         left join v2_clinic_gray_organ  on v2_clinic_gray_organ.chain_id = organ.id
                         inner join v2_clinic_region_organ on organ.id = v2_clinic_region_organ.chain_id
                where  v2_clinic_gray_organ.id is null
                    and organ.address_city_name like '%{cityName}%';
        '''.format(cityName=cityName)
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------拉{cityName}到灰度:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    def push_all_normal_organ_to_prod(self):
        sql = '''
            delete from v2_clinic_gray_organ  where env = 1 and remark is null
        '''
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------全部用户到正式:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")
    def push_non_active_organ_to_prod(self, activeDate, env):
        sql = '''
            delete  v2_clinic_gray_organ.*  
            from v2_clinic_gray_organ
                left join v2_clinic_active_chain on v2_clinic_active_chain.chain_id = v2_clinic_gray_organ.chain_id
            where (v2_clinic_active_chain.last_active_date <= '{activeDate}' or v2_clinic_active_chain.chain_id is null)
              and  v2_clinic_gray_organ.remark is   null
              and v2_clinic_gray_organ.env = 1 
        '''.format(activeDate=activeDate, env=env)
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------踢不活跃诊所(最近收费单<{activeDate})到正式:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    def check_region_id(self):
        sql = """
            update v2_clinic_gray_organ 
                    inner join v2_clinic_region_organ on v2_clinic_gray_organ.chain_id = v2_clinic_region_organ.chain_id
            set v2_clinic_gray_organ.region_id = v2_clinic_region_organ.region_id
            where v2_clinic_gray_organ.region_id != v2_clinic_region_organ.region_id;
        """
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------检查灰度表RegionId和Region表不一致:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    def check_yaozhen_futong(self,env):
        sql = """
                update  v2_clinic_cooperation_clinic  as co inner join v2_clinic_gray_organ as l on co.chain_id = l.chain_id
                inner join v2_clinic_gray_organ as r on co.cooperation_chain_id = r.chain_id
                set l.env = {env},
                    r.env = {env}
                where l.env != r.env
        """.format(env=env)
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}------检查新开通的药诊互通:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    def deal_new_yaozhen_futong(self):
        sql = '''
        insert ignore into v2_clinic_gray_organ(chain_id, name, env, region_id, his_type, remark)
        select o.id, o.name, 0, r.region_id, o.his_type, '不拉灰度：药诊互通'
        from organ as o
                 inner join v2_clinic_region_organ as r on o.id = r.chain_id
        where o.node_type = 1
          and o.id in (select chain_id
                       from v2_clinic_cooperation_clinic
                       where status = 0);
        '''
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}-----检查新开通的药诊互通主门店:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")
        sql = '''
        insert ignore into v2_clinic_gray_organ(chain_id, name, env, region_id, his_type, remark)
        select o.id, o.name, 0, r.region_id, o.his_type, '不拉灰度：药诊互通'
        from organ as o
                 inner join v2_clinic_region_organ as r on o.id = r.chain_id
        where o.node_type = 1
          and o.id in (select cooperation_chain_id
                       from v2_clinic_cooperation_clinic
                       where status = 0);
        '''
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}-----检查新开通的药诊互通合作店:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")

    def pull_active_organ_to_prod(self, activeDate, pushCount, regionId, env):
        sql = '''
            delete from  v2_clinic_gray_organ where chain_id in (
              select * from (
                select  distinct v2_clinic_gray_organ.chain_id
                from v2_clinic_gray_organ
                         inner join v2_clinic_active_chain on v2_clinic_active_chain.chain_id = v2_clinic_gray_organ.chain_id
                where v2_clinic_active_chain.last_active_date >= '{activeDate}'
                  and  v2_clinic_gray_organ.remark is  null
                  and v2_clinic_gray_organ.region_id = {regionId} 
                  and v2_clinic_gray_organ.env = 1 
                limit {pushCount} 
                ) as a
                )
       '''.format(activeDate=activeDate, pushCount=pushCount, regionId=regionId, env=env)
        affect_rows = self.db_client.execute(sql)
        print(f"{RED}----踢活跃诊所({activeDate})到正式:{RESET}{GREEN}{affect_rows}{RESET}个连锁,执行SQL:{sql}")


def main():
    parser = argparse.ArgumentParser(description='')
    parser.add_argument('--env', help='env[dev, test, pre, gray, prod]')
    parser.add_argument('--chain_id', help='连锁id',required=False, default='')
    parser.add_argument('--region_name', help='分区ID 1 上海 2杭州')
    parser.add_argument('--flush_type', help='刷数据的类型 0/None 拉连锁的刷数据模式  10刷数据模式(环境控制 + 每次执行数量控制),20拉灰度')
    parser.add_argument('--op_type', help='拉灰度操作类型',required=False, default='')
    parser.add_argument('--region1_count', help='分区1数量',required=False, default='')
    parser.add_argument('--region2_count', help='分区2数量',required=False, default='')
    parser.add_argument('--active_date', help='活跃日期',required=False, default='2025-06-01')
    parser.add_argument('--chain_id_str', help='拉idstr',required=False, default='')
    parser.add_argument('--city_name', help='city_name',required=False, default='')
    parser.add_argument('--province_name', help='province_name',required=False, default='')
    parser.add_argument('--not_pull_province_name', help='province_name',required=False, default='')
    parser.add_argument('--chain_name_str', help='拉namestr',required=False, default='')
    parser.add_argument('--flush_chain_count', help='本次要刷连锁的数量,10刷数据模式可能是一天刷点一天刷点',required=False, default='')
    parser.add_argument('--script_path', help='手动刷数据，指定路径',required=False, default='')
    args = parser.parse_args()
    abcRegion = args.region_name
    chainId = args.chain_id
    flushType = args.flush_type
    flushChainCount = 500  # 本次指定要刷连锁的数量
    scriptPath = None  # 本次指定要刷脚本的相对路径 。利用这个刷脚本的框架 管理每个连锁只刷一次
    if not args.env and not chainId:
        parser.print_help()
        sys.exit(-1)

    mm = MigrateManagment(args.env, abcRegion)
    # 拉灰度按环境刷数据
    if flushType == None or flushType == '0':
        if args.env:
            mm.migrate_by_env(args.env, chainId, abcRegion)
        else:
            parser.print_help()
            sys.exit(-1)
    elif flushType == '20':  # 拉灰度
        if args.flush_chain_count:
            flushChainCount = int(args.flush_chain_count)
        if args.script_path:
            scriptPath = args.script_path
        # 开始刷数据
        mm.pure_flushdata_by_env(args.env, chainId, scriptPath, flushChainCount, abcRegion)
    elif flushType == '30' :  # 指定脚本刷数据
        if args.op_type == '[拉灰度]按名字拉':
            mm.pull_chain_name_to_gray(args.chain_name_str,args.not_pull_province_name)
        elif args.op_type == '[拉灰度]按id拉':
            mm.pull_chain_id_to_gray(args.env, args.chain_id_str,args.not_pull_province_name)
        elif args.op_type == '[拉灰度]按数量':
            mm.pull_to_env_with_limit(args.env,1, args.region1_count,args.active_date,args.not_pull_province_name)
            mm.pull_to_env_with_limit(args.env,2, args.region2_count,args.active_date,args.not_pull_province_name)
        elif args.op_type == '[拉灰度]不活跃诊所':
            mm.pull_non_active_to_gray(args.active_date)
        elif args.op_type == '[拉灰度]大店':
            # 拉大店到灰度
            mm.pull_big_chain_to_gray()
        elif args.op_type == '[拉灰度]拉指定城市连锁':
            mm.pull_city_to_gray(args.city_name)
        elif args.op_type == '[拉灰度]拉指定省份连锁':
            mm.pull_province_to_gray(args.provice_name)
        elif args.op_type == '[拉灰度]全部拉到灰度':
            #拉全部普通门店
            mm.pull_all_normal_to_gray()
            # 检查药诊互通
            mm.check_yaozhen_futong(1)
            # 拉大店到灰度
            mm.pull_big_chain_to_gray()
        elif args.op_type == '[踢正式]按数量':
            # 踢活跃到正式
            mm.pull_active_organ_to_prod( args.active_date, args.region1_count, 1, args.env)
            mm.pull_active_organ_to_prod( args.active_date, args.region2_count, 2,args.env)
        elif args.op_type == '[踢正式]大店':
            #踢大店到正式
            mm.push_big_chain_to_prod()
        elif args.op_type == '[踢正式]不活跃诊所':
            # 不活跃到正式
            mm.push_non_active_organ_to_prod(args.active_date,args.env)
        elif args.op_type == '[踢正式]全部踢到正式':
            #踢大店到正式
            mm.push_big_chain_to_prod()
            #踢所有普通门店到正式
            mm.push_all_normal_organ_to_prod()
            # 新开的药诊复通
            mm.deal_new_yaozhen_futong()
            # 检查药诊互通
            mm.check_yaozhen_futong(0)
        elif args.op_type == '[操作]标记药诊互通':
            # 新开的药诊复通
            mm.deal_new_yaozhen_futong()
            # 检查药诊互通
            mm.check_yaozhen_futong(0)

        # 检查分区id是否写错
        mm.check_region_id()


if __name__ == '__main__':
    main()
