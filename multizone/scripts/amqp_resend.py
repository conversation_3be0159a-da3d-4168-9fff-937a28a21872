#! /usr/bin/env python3
import argparse
import hashlib
import json
import logging
import os
import time
from datetime import date, datetime

import requests
from aliyun.log import GetLogsRequest

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.log import AliyunLogClient
from multizone.amqp import RabbitMqClient, RocketMqClient
from multizone.db import DBClient
from multizone.rpc import AbcCisMonitorClient as monitor_client
from scripts.common.utils.lists import ListUtils as list_utils
from multizone.rpc.RpcClient import regionRpcHost


# f = open('message-amqp.txt', "w+")

env_map = {
    1: 'gray',
    2: 'pre'
}

def recover_sls_message_amqp(region_name, env):
    # rocketmq_cli = RocketMqClient(region_name, env, False)
    # rocketmq_cli.producer_start()
    # rabbitmq_cli = RabbitMqClient(region_name, env, False)
    db_cli = DBClient(region_name, 'ob', 'abc_cis_basic', env, False, jumper_user='jiangxf')
    rows = db_cli.fetchall(f"""select extra_data, extra_data ->> '$.businessInfo.chainId', env
from abc_cis_monitor.v2_service_alert_message m
         left join abc_cis_basic.v2_clinic_gray_organ o on m.extra_data ->> '$.businessInfo.chainId' = o.chain_id
where m.created > '2025-05-19 21:40:00'
  and title = '门诊单生成收费单消息处理失败，及时处理';""")
    for row in rows:
        logging.info(f'data: {row["extra_data"]}')
        t = int(time.time())
        logging.info(f't = {t}')
        url = f'http://{regionRpcHost("ShangHai", env_map.get(row["env"], "prod"))}/rpc/charges/ha-rabbitmq-retry'
        logging.info(url)
        rsp = json.loads(
            requests.post(
                url=url,
                headers={
                    '__region_id__': '2'
                },
                json=json.loads(row['extra_data'])
            ).content.decode('utf-8'))
        logging.info(f'rsp:{json.dumps(rsp)}')
        # try:
        #     rabbitmq_cli.send_message(exchange='cis.exchange.topic',
        #                       routing_key='',
        #                       message=row['extra_data'])
        # except Exception as e:
        #     print(e)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--region-name', help='分区')
    # parser.add_argument('--op-type', help='操作类型 pull/recover')
    args = parser.parse_args()

    # logstore = f'{args.env}_longtime' if args.env != 'prod' else 'abc-cis-retry-message'
    # log_client_factory = AliyunLogClient(args.region_name, logstore)
    # log_client = log_client_factory.client
    recover_sls_message_amqp(args.region_name, args.env)
