import json

# 假设你的文件名为 'data.json'
filename = '11.json'

# 读取文件内容
with open(filename, 'r', encoding='utf-8') as file:
    data = json.load(file)

# 提取数据并生成目标格式
# result = {}
# for item in data:
#     for prescription_item in item["prescriptionFormItems"]:
#         keyId = prescription_item["keyId"]
#         currentUnitPrice = prescription_item["currentUnitPrice"]
#         fractionPrice = prescription_item["fractionPrice"]
#         result[keyId] = {
#             "currentUnitPrice": currentUnitPrice,
#             "fractionPrice": fractionPrice
#         }
# # 打印生成的 JSON 数组
# print(json.dumps(result, indent=2))


sorted_data = dict(sorted(data.items(), key=lambda item: item[0]))
print(json.dumps(sorted_data, indent=2))