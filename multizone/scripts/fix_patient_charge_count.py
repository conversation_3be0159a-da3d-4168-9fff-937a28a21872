#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修正患者收费次数脚本
按连锁逐个查询，找到不一致的数据，最后更新到 business_stat 表中
作者: yinxiaoyang
日期: 2025-05-29
"""

import argparse
import os
import sys
import time

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.config.region import region_name_id_map
from multizone.db import DBClient
from multizone.log import AliyunLogClient
from idwork import IdWork


def get_db_client(region_name='ShangHai', env='prod', logger=None):
    """获取数据库连接"""
    try:
        # 使用 ob 集群连接到 abc_cis_charge 数据库
        charge_client = DBClient(region_name, 'ob', 'abc_cis_charge', env, True)
        patient_client = DBClient(region_name, 'abc_cis_account_base', 'abc_cis_patient', env, True)
        return charge_client, patient_client
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        sys.exit(1)


def get_all_chains(logger, ob_client, region_name):
    """获取所有连锁ID"""
    region_id = region_name_id_map.get(region_name)
    try:
        sql = """
        select a.id as chain_id
        from abc_cis_basic.organ a
                 inner join abc_cis_basic.v2_clinic_region_organ b on (a.id = b.chain_id)
        where a.his_type = 1
          and b.region_id = {region_id}
        order by a.id;        
        """.format(region_id=region_id)
        chains = ob_client.fetchall(sql)
        return [chain['chain_id'] for chain in chains]
    except Exception as e:
        logger.error(f"获取连锁列表失败: {str(e)}")
        return []


def check_charge_count_inconsistency(logger, ob_client, chain_id):
    """检查指定连锁的收费次数不一致数据"""
    try:
        sql = f"""
        select count(1) as cnt, cs.chain_id, cs.clinic_id, cs.patient_id
        from abc_cis_charge.v2_charge_sheet cs
                 left join abc_cis_patient.v2_patient_business_stat b on (cs.patient_id = b.patient_id and cs.chain_id = b.chain_id and cs.clinic_id = b.clinic_id)
        where cs.is_deleted = 0
          and cs.is_closed = 0
          and (cs.type not in (5, 10, 11) and cs.status > 1)
          and cs.chain_id = '{chain_id}'
          and cs.patient_id != '0000000000000000000000000000'
        group by cs.clinic_id, cs.patient_id
        having max(b.id) is null or cnt != max(b.charge_count)
        """

        inconsistent_data = ob_client.fetchall(sql)
        return inconsistent_data
    except Exception as e:
        logger.error(f"检查连锁 {chain_id} 收费次数不一致数据失败: {str(e)}")
        return []


def update_business_stat_charge(logger, patient_client, data, id_work):
    """更新 business_stat 表中的 charge_count"""
    try:

        sql = f"""
            insert into v2_patient_business_stat (id,
                                                  patient_id,
                                                  clinic_id,
                                                  charge_count,
                                                  outpatient_count,
                                                  chain_id,
                                                  created,
                                                  created_by,
                                                  last_modified,
                                                  last_modified_by)
            values ('{id_work.getUIDLong()}',
                    '{data['patient_id']}',
                    '{data['clinic_id']}',
                    {data['cnt']},
                    0,
                    '{data['chain_id']}',
                    now(),
                    '0000000000000000000000000000',
                    now(),
                    '0000000000000000000000000000')
                    on duplicate key update charge_count = values(charge_count)
        """
        logger.info(sql)
        result = patient_client.execute(sql)
        return result
    except Exception as e:
        logger.error(f"更新 clinicId:{data['clinic_id']} patientId:{data['patient_id']} 失败: {str(e)}")
        return 0


def process_chain_charge_count(logger, chain_id, patient_client, ob_client, id_work):
    """处理单个连锁的数据修正"""

    # 检查不一致的数据
    inconsistent_data = check_charge_count_inconsistency(logger, ob_client, chain_id)

    if not inconsistent_data:
        logger.info(f"连锁 {chain_id}: 没有发现 charge_count 不一致的数据")
        return 0

    logger.info(f"连锁 {chain_id}: charge_count 发现 {len(inconsistent_data)} 条不一致的数据")

    updated_count = 0
    skipped_count = 0
    failed_count = 0

    for data in inconsistent_data:
        result = update_business_stat_charge(logger, patient_client, data, id_work)
        if result > 0:
            updated_count += 1
        else:
            failed_count += 1

    # 合并日志到一行
    logger.info(f"连锁 {chain_id}: 发现 charge_count 不一致 {len(inconsistent_data)} 条, 更新成功 {updated_count} 条, 跳过 {skipped_count} 条, 失败 {failed_count} 条")
    return updated_count


def update_business_stat_outpatient(logger, patient_client, data, id_work):
    """更新 business_stat 表中的 charge_count"""
    try:
        sql = f"""
            insert into v2_patient_business_stat (id,
                                                  patient_id,
                                                  clinic_id,
                                                  charge_count,
                                                  outpatient_count,
                                                  chain_id,
                                                  created,
                                                  created_by,
                                                  last_modified,
                                                  last_modified_by)
            values ('{id_work.getUIDLong()}',
                    '{data['patient_id']}',
                    '{data['clinic_id']}',
                    0,
                    {data['cnt']},
                    '{data['chain_id']}',
                    now(),
                    '0000000000000000000000000000',
                    now(),
                    '0000000000000000000000000000')
                    on duplicate key update outpatient_count = values(outpatient_count)
        """
        logger.info(sql)
        result = patient_client.execute(sql)
        return result
    except Exception as e:
        logger.error(f"更新 clinicId:{data['clinic_id']} patientId:{data['patient_id']} 失败: {str(e)}")
        return 0


def check_outpatient_count_inconsistency(logger, ob_client, chain_id):
    """检查指定连锁的收费次数不一致数据"""
    try:
        sql = f"""
            select count(1) as cnt, os.chain_id, os.clinic_id, os.patient_id
            from abc_cis_outpatient.v2_outpatient_sheet os
                     left join abc_cis_patient.v2_patient_business_stat p on (os.clinic_id = p.clinic_id and os.patient_id = p.patient_id)
            where os.is_deleted = 0
              and os.status = 1
              and os.chain_id = '{chain_id}'
              and os.patient_id != '0000000000000000000000000000'
            group by os.clinic_id, os.patient_id
            having max(p.id) is null or cnt != max(p.outpatient_count);
        """

        inconsistent_data = ob_client.fetchall(sql)
        return inconsistent_data
    except Exception as e:
        logger.error(f"检查连锁 {chain_id} 收费次数不一致数据失败: {str(e)}")
        return []


def process_chain_outpatient_count(logger, chain_id, patient_client, ob_client, id_work):
    """处理单个连锁的数据修正"""

    # 检查不一致的数据
    inconsistent_data = check_outpatient_count_inconsistency(logger, ob_client, chain_id)

    if not inconsistent_data:
        logger.info(f"连锁 {chain_id}: 没有发现 outpatient_count 不一致的数据")
        return 0

    logger.info(f"连锁 {chain_id}: outpatient_count 发现 {len(inconsistent_data)} 条不一致的数据")

    updated_count = 0
    skipped_count = 0
    failed_count = 0

    for data in inconsistent_data:
        result = update_business_stat_outpatient(logger, patient_client, data, id_work)
        if result > 0:
            updated_count += 1
        else:
            failed_count += 1

    # 合并日志到一行
    logger.info(f"连锁 {chain_id}: 发现 outpatient_count 不一致 {len(inconsistent_data)} 条, 更新成功 {updated_count} 条, 跳过 {skipped_count} 条, 失败 {failed_count} 条")
    return updated_count


def main():
    parser = argparse.ArgumentParser(description='修正患者收费次数')
    parser.add_argument('--env', default='prod', help='环境 (prod/test/dev)')
    parser.add_argument('--region', default='ShangHai', help='区域 (ShangHai/HangZhou)')
    parser.add_argument('--chain-id', help='指定连锁ID，如果不指定则处理所有连锁')
    parser.add_argument('--limit', type=int, help='限制处理的连锁数量')

    args = parser.parse_args()

    logger = AliyunLogClient(args.region, 'prod').logger

    # 获取数据库连接
    ob_client, patient_client = get_db_client(args.region, args.env, logger)
    id_work = IdWork(patient_client, False)
    id_work.config()

    # 获取要处理的连锁列表
    if args.chain_id:
        chain_ids = [args.chain_id]
        chain_info = f"指定连锁: {args.chain_id}"
    else:
        chain_ids = get_all_chains(logger, ob_client, args.region)
        if args.limit:
            chain_ids = chain_ids[:args.limit]
        chain_info = f"获取到 {len(chain_ids)} 个连锁" + (f" (限制前{args.limit}个)" if args.limit else "")

    if not chain_ids:
        logger.error("没有找到要处理的连锁")
        return

    # 合并启动信息到一行
    logger.info(f"开始执行患者收费次数修正 - 环境: {args.env}, 区域: {args.region}, {chain_info}")

    total_updated = 0
    failed_chains = 0
    start_time = time.time()

    for i, chain_id in enumerate(chain_ids, 1):
        try:
            updated_count = process_chain_charge_count(logger, chain_id, patient_client, ob_client, id_work)
            total_updated += updated_count

            updated_count = process_chain_outpatient_count(logger, chain_id, patient_client, ob_client, id_work)
            total_updated += updated_count

            # 每处理10个连锁休息一下并输出进度
            if i % 10 == 0:
                progress_pct = (i / len(chain_ids)) * 100
                logger.info(f"进度: {i}/{len(chain_ids)} ({progress_pct:.1f}%), 累计更新: {total_updated} 条")
                time.sleep(1)

        except Exception as e:
            failed_chains += 1
            logger.error(f"处理连锁 {chain_id} 时发生错误:", e)
            continue

    end_time = time.time()
    elapsed_time = end_time - start_time

    # 合并结果信息到一行
    logger.info(f"处理完成! 总连锁: {len(chain_ids)}, 失败: {failed_chains}, 实际更新: {total_updated} 条, 耗时: {elapsed_time:.2f}秒")


if __name__ == '__main__':
    main()
