import os
import sys

from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_ververica20220718 import models as ververica_20220718_models
from alibabacloud_ververica20220718.client import Client as ververica20220718Client

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.rpc import AbcCisMonitorClient as monitor

try:
    from multizone.log import AliyunLogClient
except Exception as e:
    pass

"""

后台 flink 监控脚本

1. 监控配置的 flink 实例是否正在运行
2. 监控配置的 flink 延迟情况


依赖


pip install alibabacloud_tea_openapi==0.3.12 
pip install alibabacloud_tea_util=0.3.13 
pip install alibabacloud_openapi_util==0.2.2 
pip install alibabacloud_endpoint_util==0.0.3 
pip install alibabacloud_gateway_spi==0.0.2
pip install alibabacloud_gateway_pop==0.0.6
pip install alibabacloud_openapi_paginator==1.0.1
pip install alibabacloud_credentials==0.3.5
pip install alibabacloud_tea_xml==0.0.2
pip install alibabacloud_darabonba_encode_util==0.0.2
pip install alibabacloud_darabonba_signature_util==0.0.4
pip install alibabacloud_darabonba_string==0.0.4
pip install alibabacloud_darabonba_map==0.0.1
pip install alibabacloud_darabonba_array==0.1.0
pip install alibabacloud_ververica20220718==1.7.0

"""

flink_config = {
    'ShangHai': {
        'accessKeyId': 'LTAI5tSTWhGfehVJFjxgjSET',
        'accessKeySecret': '******************************',
        'endpoint': 'ververica.cn-shanghai.aliyuncs.com',
        'workspace': 'eff1ecb1892042',
        'namespace': 'abc-prod-backend-team-flink-default',
        'monitorDeployment': [
            {
                'id': '4c41652b-9802-4606-9591-81e74da65eb9',
                'name': 'examination-es-index-stream',
            },
            {
                'id': '08ba0b75-0c28-4648-adfc-082bd17f882e',
                'name': 'charge-es-index-stream',
            },
            {
                'id': 'a4c62b12-b201-4292-bbe1-59775b7d111c',
                'name': 'crm-patient-business-stat-stream',
            }
        ]
    },
    'HangZhou': {
        'accessKeyId': 'LTAI5tSTWhGfehVJFjxgjSET',
        'accessKeySecret': '******************************',
        'endpoint': 'ververica.cn-hangzhou.aliyuncs.com',
        'workspace': 'fe34315641074f',
        'namespace': 'abc-prod-backend-team-flink-hz-default',
        'monitorDeployment': [
            {
                'id': 'f47720d0-a3ed-480c-b98c-f13712da49b8',
                'name': 'examination-es-index-stream'
            },
            {
                'id': 'fdbe9ff9-238e-483f-935d-31b93248d3ec',
                'name': 'charge-es-index-stream'
            },
            {
                'id': 'f169716d-8e68-47e8-ab31-db9794282ac6',
                'name': 'crm-patient-business-stat-stream',
            },
            {
                'id': 'c4df616c-902f-4fd2-8d0b-52f2f058fbc4',
                'name': 'outpatient-quicklist-es-index-stream'
            }
        ]
    }
}


def extract_data(resp):
    if not resp or not resp.body:
        raise Exception('resp is None')

    if resp.body.success:
        return resp.body.data
    else:
        raise Exception('request failed, error_code: {}, error_message: {}', resp.body.error_code, resp.body.error_message)


class FlinkMonitor:
    logger = None

    @staticmethod
    def create_client(accessKeyId, accessKeySecret, endpoint) -> ververica20220718Client:
        """
        使用AK&SK初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config(
            type='access_key',
            access_key_id=accessKeyId,
            access_key_secret=accessKeySecret
        )
        # Endpoint请根据实际情况修改
        config.endpoint = endpoint
        return ververica20220718Client(config)

    def monitor(self, region):
        # try:
        #     self.logger = AliyunLogClient(region, 'prod').logger
        # except Exception as e:
        #     print(f'初始化日志客户端失败')

        for deployment in flink_config[region]['monitorDeployment']:
            # 获取 deployment 的最新 job
            client = FlinkMonitor.create_client(flink_config[region]['accessKeyId'], flink_config[region]['accessKeySecret'], flink_config[region]['endpoint'])
            job_list_info = self.get_last_deployment_job(client, deployment['id'], flink_config[region]['workspace'], flink_config[region]['namespace'])
            if not job_list_info:
                self.warn(f"没有找到 name:{deployment['name']} deploymentId:{deployment['id']} job")
                continue

            # 获取 job 的状态
            job_info = self.get_job_info(client, job_list_info.job_id, flink_config[region]['workspace'], flink_config[region]['namespace'])
            self.info("job_info: {}", job_info.job_id)
            if not job_info:
                self.warn(f"没有找到 name:{deployment['name']} deploymentId:{deployment['id']} job:{job_list_info.id} 信息")
                continue

            # 如果 job 的状态为 FINISHED 或 FAILED 则重启该实例并触发告警
            job_status = job_info.status.current_job_status
            if job_status == 'FINISHED' or job_status == 'FAILED':  # or job_status == 'CANCELLED':
                self.error(f"job:{job_info.job_id} 状态为 {job_status}，需要重启")
                # 重启
                try:
                    # 重启成功
                    new_job = self.start_job(client, deployment['id'], flink_config[region]['workspace'], flink_config[region]['namespace'])
                    monitor.sendServiceAlertMessage(f'[{region}]' + deployment['name'] + " REAL-TIME TASK FAILED", f"jobIb:{job_info.job_id} 状态为 {job_status}，已经重启，新jobId: {new_job.job_id}")
                except Exception as error:
                    # 重启失败
                    monitor.sendServiceAlertMessage(f'[{region}]' + deployment['name'] + "REAL-TIME TASK FAILED", f"job:{job_info.job_id} 状态为 {job_status}，重启失败，错误信息: {error}")
            else:
                self.info(f"deploymentName:{deployment['name']} job:{job_info.job_id} 运行正常状态为 {job_status}，不需要重启")

    def get_last_deployment_job(self, flink_client, deployment_id, workspace, namespace):
        list_jobs_headers = ververica_20220718_models.ListJobsHeaders(
            workspace=workspace,
        )
        list_jobs_request = ververica_20220718_models.ListJobsRequest(
            deployment_id=deployment_id,
            page_index=1,
            page_size=1
        )
        runtime = util_models.RuntimeOptions()
        resp = None
        try:
            # 复制代码运行请自行打印 API 的返回值
            resp = flink_client.list_jobs_with_options(namespace, list_jobs_request, list_jobs_headers, runtime)
        except Exception as error:
            self.error("get_last_deployment_job error", error)
            raise Exception(error)

        list = extract_data(resp)

        return list[0] if list else None

    def get_job_info(self, flink_client, job_id, workspace, namespace):

        get_job_headers = ververica_20220718_models.GetJobHeaders(
            workspace=workspace,
        )
        runtime = util_models.RuntimeOptions()
        resp = None
        try:
            # 复制代码运行请自行打印 API 的返回值
            resp = flink_client.get_job_with_options(namespace, job_id, get_job_headers, runtime)
        except Exception as error:
            self.error("get_job_info error", error)
            raise Exception(error)

        return extract_data(resp)

    def start_job(self, client, deployment_id, workspace, namespace):
        start_job_with_params_headers = ververica_20220718_models.StartJobWithParamsHeaders(
            workspace=workspace
        )
        job_start_parameters_deployment_restore_strategy = ververica_20220718_models.DeploymentRestoreStrategy(
            # 最新状态启动
            kind='LATEST_STATE',
            # 是否允许忽略部分算子状态
            allow_non_restored_state=False
        )
        job_start_parameters = ververica_20220718_models.JobStartParameters(
            deployment_id=deployment_id,
            restore_strategy=job_start_parameters_deployment_restore_strategy
        )
        start_job_with_params_request = ververica_20220718_models.StartJobWithParamsRequest(
            body=job_start_parameters
        )
        runtime = util_models.RuntimeOptions()
        resp = None
        try:
            # 复制代码运行请自行打印 API 的返回值
            resp = client.start_job_with_params_with_options(namespace, start_job_with_params_request, start_job_with_params_headers, runtime)
        except Exception as error:
            self.error(error)
            raise Exception(error)

        return extract_data(resp)

    def info(self, msg, *args, **kwargs):
        if self.logger:
            self.logger.info(msg, *args, **kwargs)
        else:
            print(msg)

    def error(self, msg, *args, **kwargs):
        if self.logger:
            self.logger.error(msg, *args, **kwargs)
        else:
            print(msg)

    def warn(self, msg, *args, **kwargs):
        if self.logger:
            self.logger.warn(msg, *args, **kwargs)
        else:
            print(msg)


def main():
    flink_monitor = FlinkMonitor()
    flink_monitor.monitor('ShangHai')
    flink_monitor.monitor('HangZhou')


if __name__ == '__main__':
    main()
