#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import sys
import time
from datetime import datetime, timedelta
from aliyun.log import LogClient, GetLogsRequest
from prettytable import PrettyTable

# Configuration
# Replace these values with your own
CONFIG = {
    'endpoint': 'your-endpoint.log.aliyuncs.com',  # e.g., cn-hangzhou.log.aliyuncs.com
    'access_key_id': 'your-access-key-id',
    'access_key_secret': 'your-access-key-secret',
    'project': 'your-project-name',
    'logstore': 'your-logstore-name',
    'query': '',  # Optional: filter logs with a query
    'from_time': int((datetime.now() - timedelta(hours=24)).timestamp()),  # Default: 24 hours ago
    'to_time': int(datetime.now().timestamp()),  # Default: now
    'limit': 100,  # Maximum number of logs to retrieve
    'output_format': 'table',  # Options: 'table', 'json', 'csv'
    'output_file': None,  # Optional: output file path (None for stdout)
}

def get_client():
    """Create and return an SLS client"""
    return LogClient(CONFIG['endpoint'], CONFIG['access_key_id'], CONFIG['access_key_secret'])

def fetch_logs(client):
    """Fetch logs from SLS"""
    request = GetLogsRequest(
        CONFIG['project'], 
        CONFIG['logstore'], 
        CONFIG['from_time'], 
        CONFIG['to_time'], 
        topic='', 
        query=CONFIG['query'], 
        line=CONFIG['limit'], 
        offset=0, 
        reverse=True
    )
    
    try:
        response = client.get_logs(request)
        return response.get_logs()
    except Exception as e:
        print(f"Error fetching logs: {e}")
        sys.exit(1)

def extract_fields(log):
    """Extract chainId, clinicId, employeeId, and IP address from log"""
    contents = log.get_contents()
    message = contents.get('message', '')
    
    # Extract IP address
    ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', message)
    ip_address = ip_match.group(1) if ip_match else None
    
    # Extract chainId, clinicId, employeeId
    chain_id_match = re.search(r'chainId:([^,\s]+)', message)
    clinic_id_match = re.search(r'clinicId:([^,\s]+)', message)
    employee_id_match = re.search(r'employeeId:([^,\s]+)', message)
    
    chain_id = chain_id_match.group(1) if chain_id_match else None
    clinic_id = clinic_id_match.group(1) if clinic_id_match else None
    employee_id = employee_id_match.group(1) if employee_id_match else None
    
    return {
        'timestamp': log.get_time(),
        'source': log.get_source(),
        'ip_address': ip_address,
        'chain_id': chain_id,
        'clinic_id': clinic_id,
        'employee_id': employee_id
    }

def format_output(extracted_logs):
    """Format and output the extracted logs"""
    if not extracted_logs:
        print("No logs found matching the criteria")
        return
    
    format_type = CONFIG['output_format']
    output_file = CONFIG['output_file']
    
    if format_type == 'json':
        import json
        output = json.dumps(extracted_logs, indent=2, ensure_ascii=False)
    elif format_type == 'csv':
        import csv
        import io
        
        output_buffer = io.StringIO()
        fieldnames = ['timestamp', 'source', 'ip_address', 'chain_id', 'clinic_id', 'employee_id']
        writer = csv.DictWriter(output_buffer, fieldnames=fieldnames)
        
        writer.writeheader()
        for log in extracted_logs:
            writer.writerow(log)
        
        output = output_buffer.getvalue()
    else:  # table format
        table = PrettyTable()
        table.field_names = ['Timestamp', 'Source', 'IP Address', 'Chain ID', 'Clinic ID', 'Employee ID']
        
        for log in extracted_logs:
            timestamp = datetime.fromtimestamp(log['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
            table.add_row([
                timestamp,
                log['source'],
                log['ip_address'],
                log['chain_id'],
                log['clinic_id'],
                log['employee_id']
            ])
        
        output = table.get_string()
    
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(output)
        print(f"Results written to {output_file}")
    else:
        print(output)

def main():
    # Create SLS client
    client = get_client()
    
    # Fetch logs
    logs = fetch_logs(client)
    
    # Extract fields from logs
    extracted_logs = [extract_fields(log) for log in logs]
    
    # Format and output results
    format_output(extracted_logs)

if __name__ == '__main__':
    main()
