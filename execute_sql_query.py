#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
执行SQL查询脚本
连接杭州OceanBase数据库，执行指定SQL查询，并输出结果
"""

import os
import sys
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(CURRENT_DIR))

from multizone.db import DBClient

def get_db_client(region_name='ShangHai', cluster='ob', database='abc_cis_goods_log', env='prod'):
    """获取数据库连接"""
    try:
        return DBClient(region_name, cluster, database, env, False,'robinsli')
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        sys.exit(1)

def execute_update_statements_query(db_client, chain_ids):
    """执行查询生成更新语句的SQL"""
    chain_ids_str = "'" + "','".join(chain_ids) + "'"
    # 循环 chain_ids
    for chain_id in chain_ids:
        sql = f"""
    SELECT  /*+ query_timeout(100000000)  */
        CONCAT(
            'UPDATE abc_cis_goods_log.v2_goods_stock_log ',
            'SET goods = JSON_SET(goods, ''$.traceableCodeList'', ',
            'JSON_ARRAY(',
            GROUP_CONCAT(
                DISTINCT
                CONCAT(
                    'JSON_OBJECT(''no'', ''',
                    no,
                    ''', ''count'', ',
                    IFNULL(stock_in_no_count, 1),
                    ')'
                )
                ORDER BY no
                SEPARATOR ', '
            ),
            ')) WHERE id = ',
            l.id, ';'
        ) AS update_statement
    FROM
        abc_cis_goods_log.v2_goods_stock_log AS l
    INNER JOIN (
        SELECT
            chain_id,
            stock_in_order_item_id,
            no,
            stock_in_no_count
        FROM
            abc_cis_goods.v2_goods_stock_traceable_code
        WHERE
            chain_id ='{chain_id}'
            AND stock_in_order_id IN (
                SELECT DISTINCT order_id
                FROM abc_cis_goods_Log.v2_goods_stock_log AS l
                WHERE
                    chain_id ='{chain_id}'
                    AND action = '采购入库'
                    AND created_date >= '2025-04-28'
                    AND goods->'$.traceableCodeList' IS NULL
            )
    ) AS t
    ON
        l.chain_id = t.chain_id
        AND l.order_detail_id = t.stock_in_order_item_id
        AND l.action = '采购入库'
    WHERE
        l.chain_id ='{chain_id}'
        and l.created_date >='2025-04-28'
        and l.goods->'$.traceableCodeList' IS NULL
    GROUP BY
        l.id;
    """
    
        results = db_client.fetchall(sql)
        #遍历 results
        for result in results:
            update_statement = result['update_statement']
            print(update_statement)
            # 写入文件
            with open('./shanghai_sql.txt', 'a', encoding='utf-8') as f:
                f.write(f"{update_statement}\n")



def main():
    parser = argparse.ArgumentParser(description='执行SQL查询并输出结果')
    parser.add_argument('--output', help='输出文件路径', default='sql_results.sql')
    parser.add_argument('--region', help='区域名称', default='ShangHai')
    parser.add_argument('--env', help='环境', default='prod')
    args = parser.parse_args()
    
    # 连锁列表
    chain_ids = [

                                                                                'ffffffff000000001cf00d2809f48000',
        '2c7b594a44ff4abd97bf6286612e79d2', 'ffffffff00000000123b349806b86000', 'ffffffff0000000012c7302006d6c000',
        'ffffffff000000001e0f21980a08a000', 'ffffffff000000001e0415d00a08c000', 'ffffffff00000000297716780c800001',
        'ffffffff00000000347e4e45e2df4000', 'ffffffff000000001e3063c80a0d2000', 'ffffffff00000000290e10700be70001',
        '3e2ae7c8ecfd477aa1e4e5f5ae5e205a', 'ffffffff000000001a23291809100000', 'ffffffff00000000347125c15eb58000',
        'ffffffff0000000007baca2001f50000', 'ffffffff000000003485e0eee3f14001', 'ffffffff0000000012740cb806b86000',
        'ffffffff000000001798820008388000', 'ffffffff000000001945f94008d02000', 'ffffffff00000000263873a00aa94000',
        'ffffffff000000002a6a15200c800001', 'ffffffff00000000346ffd4ffeb54000', 'ffffffff00000000347b7b99e2350001',
        'ffffffff0000000034984ede15658001', '24796ba8c806404a8ce9dc7ba140c08f', '34cc0f44696b405bb5b8578d60216e1c',
        '62ec609efcfc4e5d84b7a391e185cf60', 'ccda30cb6bf84209a9f9c08791601c0f', 'ffffffff0000000004588fa800e1a000',
        'ffffffff00000000069de5f8018b0000', 'ffffffff0000000008173d2801f96000', 'ffffffff0000000008f614e8026ac001',
        'ffffffff0000000009bfc0b002ac6000', 'ffffffff0000000009e7b85802eac000', 'ffffffff000000000a514b38030ea000',
        'ffffffff000000000af118f003548000', 'ffffffff000000000b048a8803548000', 'ffffffff000000000b2606080383c000',
        'ffffffff000000000bce11e003be2000', 'ffffffff000000000be4eb8803e22000', 'ffffffff000000000d0097c804aa4000',
        'ffffffff000000000d69a0d004cba001', 'ffffffff000000000dc192e005142000', 'ffffffff000000000dd44608052e8000',
        'ffffffff000000000e1d42f005502000', 'ffffffff000000000e2657600530c000', 'ffffffff000000000e3afa8805702000',
        'ffffffff000000000e84e16005a56000', 'ffffffff000000000fda62e805a54000', 'ffffffff0000000010416e78062ec000',
        'ffffffff000000001089e838062e8000', 'ffffffff0000000011521870067a8000', 'ffffffff000000001153ad18067ac000',
        'ffffffff0000000011b43220067ac000', 'ffffffff000000001246457006b80000', 'ffffffff00000000128f8c9806d6a000',
        'ffffffff00000000143609c807218000', 'ffffffff00000000148d580007758000', 'ffffffff00000000151e803007758000',
        'ffffffff00000000155cfde0079cc000', 'ffffffff0000000016a7e7400810e000', 'ffffffff0000000017cde59008388000',
        'ffffffff0000000017e9b87008386000', 'ffffffff00000000187ff5e0089da000', 'ffffffff00000000197dfc10090fe000',
        'ffffffff0000000019c75c4809100000', 'ffffffff000000001a067e30090fe000', 'ffffffff000000001a06d2b8090fe000',
        'ffffffff000000001a3c3ad0090fe000', 'ffffffff000000001a42e70809100000', 'ffffffff000000001a4fa238096b8000',
        'ffffffff000000001a5b0918096b8000', 'ffffffff000000001adae0e0096ba000', 'ffffffff000000001aeda7d0096b8000',
        'ffffffff000000001b769df009b50000', 'ffffffff000000001b7814d009b52000', 'ffffffff000000001beb28a009c40000',
        'ffffffff000000001c3ff17809d50000', 'ffffffff000000001cc6f3a809ece000', 'ffffffff000000001cc8631009ed2000',
        'ffffffff000000001cd2cb6009ed2000', 'ffffffff000000001e0cc2380a08a000', 'ffffffff000000001e50e1500a0d2000',
        'ffffffff000000001ee32ee00a1e6000', 'ffffffff000000001fbf4f300a244000', 'ffffffff000000001fd24c980a23c000',
        'ffffffff00000000201efb700a2e2000', 'ffffffff00000000207316280a2e2000', 'ffffffff00000000207481880a2e2000',
        'ffffffff0000000020d105380a2e4000', 'ffffffff00000000214ef0800a352000', 'ffffffff0000000021666eb80a2e2000',
        'ffffffff0000000021bb2f400a450000', 'ffffffff0000000021c590f80a450000', 'ffffffff0000000021cf7c280a450000',
        'ffffffff0000000021e29e180a44e001', 'ffffffff00000000222b8de00a4a4001', 'ffffffff0000000022414df00a4a0000',
        'ffffffff00000000224c3b080a4a0001', 'ffffffff00000000227919080a4a4000', 'ffffffff0000000022b586f80a4a0001',
        'ffffffff00000000230dc1180a56e000', 'ffffffff00000000234907200a570001', 'ffffffff0000000023546f000a56e001',
        'ffffffff0000000023763a100a5ce001', 'ffffffff0000000023bd40300a5cc000', 'ffffffff0000000023de8f580a5ce000',
        'ffffffff0000000023f183800a5cc000', 'ffffffff000000002411fa180a5cc000', 'ffffffff000000002429c9400a5ce001',
        'ffffffff0000000024afdc800a5cc000', 'ffffffff0000000024bb88080a5cc001', 'ffffffff0000000024db2c880a5cc000',
        'ffffffff0000000024defdc00a5ce001', 'ffffffff0000000024f068300a5cc000', 'ffffffff0000000025441fd00a7c4001',
        'ffffffff00000000256324e00a7c4001', 'ffffffff00000000257965b00a7c2000', 'ffffffff00000000257a23200a7c2001',
        'ffffffff00000000258563d00a7c4001', 'ffffffff0000000025c1aa300a7c2000', 'ffffffff000000002602d9200a7c4001',
        'ffffffff00000000261617d80a7c4001', 'ffffffff00000000262144e80a7c4000', 'ffffffff000000002638a5c80aa6a000',
        'ffffffff0000000026be53d00acb4000', 'ffffffff0000000026c1a0100acb6001', 'ffffffff0000000026e051d80acb6001',
        'ffffffff0000000026fe5b380acb6001', 'ffffffff0000000027098f300acb4001', 'ffffffff00000000271e57780acb6000',
        'ffffffff0000000027f07b080b580000', 'ffffffff0000000028114fd80b580001', 'ffffffff00000000284fec780b57e001',
        'ffffffff0000000028a4f8280be7c000', 'ffffffff0000000028e578700be7c000', 'ffffffff0000000028ef2e500be70001',
        'ffffffff0000000029182f280be7c000', 'ffffffff00000000295932c00c800001', 'ffffffff0000000029839dd00c7f0001',
        'ffffffff0000000029d761200c7f0000', 'ffffffff0000000029e0b6980c7f0000', 'ffffffff0000000029ec9a600c7f0001',
        'ffffffff000000002a37d3e80c800001', 'ffffffff000000002a37edd00c800001', 'ffffffff000000002a605a880c800001',
        'ffffffff000000002aa185080c800001', 'ffffffff000000002aa19bb00c7f0001', 'ffffffff000000002aa1fd580c800000',
        'ffffffff000000002aab13600c7f0001', 'ffffffff000000002abe53080d73e001', 'ffffffff000000002b49cc800ddde001',
        'ffffffff000000002b516a780ddde000', 'ffffffff000000002b70c2500ddde001', 'ffffffff000000002b7e4f300ddde001',
        'ffffffff000000002b93ddf80ddde001', 'ffffffff000000002ba5b0280e2c4000', 'ffffffff0000000034693a9e5ca24000',
        'ffffffff00000000346a81a6fca24001', 'ffffffff00000000346cd60add974000', 'ffffffff00000000346dfa86de420001',
        'ffffffff00000000346fc7abfeb58001', 'ffffffff0000000034704368beb58000', 'ffffffff00000000347078c6deb54001',
        'ffffffff0000000034707d183eb54001', 'ffffffff00000000347142e2deb58001', 'ffffffff0000000034731f3b1eb54000',
        'ffffffff00000000347376d79eb54000', 'ffffffff000000003473c1197eb58001', 'ffffffff0000000034750fdedeb54000',
        'ffffffff0000000034758c0cdeb54000', 'ffffffff000000003476afed5eb54001', 'ffffffff0000000034780e95219a8000',
        'ffffffff00000000347862f6a1990001', 'ffffffff000000003479366161b9c001', 'ffffffff00000000347a5caf62350001',
        'ffffffff00000000347b579c6234c001', 'ffffffff00000000347bb0a56207c001', 'ffffffff00000000347c7dc9a2a74001',
        'ffffffff00000000347cac0422a70001', 'ffffffff00000000347cd1c962cc4001', 'ffffffff00000000347d736c62dec000',
        'ffffffff00000000347e212422de8001', 'ffffffff00000000348019df238f0001', 'ffffffff000000003481123ea3a4c000',
        'ffffffff00000000348170f1e3ef0000', 'ffffffff000000003481765923f04000', 'ffffffff0000000034829dfae3ef4001',
        'ffffffff000000003483621e63f14001', 'ffffffff000000003483b5d1a3ee0001', 'ffffffff000000003483df3423ef4001',
        'ffffffff0000000034840a70e3f14001', 'ffffffff00000000348415b523ee0001', 'ffffffff000000003484638d23ef4000',
        'ffffffff0000000034853e48a3f14000', 'ffffffff0000000034856799a3f14000', 'ffffffff0000000034856cf823ee0000',
        'ffffffff0000000034870e3fa3f0c001', 'ffffffff000000003487b832269dc001', 'ffffffff000000003487ffbae69e0001',
        'ffffffff000000003488d29f269d4000', 'ffffffff00000000348a5777a69dc001', 'ffffffff00000000348ad333a69d0001',
        'ffffffff00000000348ade33669d4001', 'ffffffff00000000348b4cfce69d0001', 'ffffffff00000000348b4fb2a69d4000',
        'ffffffff00000000348b806c669e4000', 'ffffffff00000000348b9c01e69d0000', 'ffffffff00000000348b9f84269d0000',
        'ffffffff00000000348d1cde8a470000', 'ffffffff00000000348dcd246a46c001', 'ffffffff00000000348e6cc64b3fc001',
        'ffffffff00000000348f4128cbf50001', 'ffffffff0000000034906971ace80001', 'ffffffff000000003490c4618d554001',
        'ffffffff0000000034919196cd564001', 'ffffffff0000000034927004eefec000', 'ffffffff000000003496e66d13978000',
        'ffffffff0000000034992dc736218000', 'ffffffff000000003499fa26d71a4001', 'ffffffff00000000349e3faddb3a0001',
        'ffffffff0000000034a0dfc65dc64002', 'ffffffff0000000034a212c99f890001', 'ffffffff0000000034a286f99f85c001',
        'ffffffff00000000219cd8480a450000', 'ffffffff00000000243d8f580a5ce001', 'ffffffff00000000346fc8c31eb54001',
        'ffffffff000000003484177be3ee0001', 'ffffffff0000000007b8cb7801f50001', 'ffffffff000000001a4db6f8096ba000',
        'ffffffff000000002728a9200acb6000', 'ffffffff000000003476093a3eb54001'
    ]
    
    # 获取数据库连接
    db_client = get_db_client(args.region, 'ob', 'abc_cis_goods_log', args.env)
    
    try:
        print(f"[INFO] 开始执行SQL查询，连锁数量: {len(chain_ids)}")
        
        # 执行更新语句查询
        update_results = execute_update_statements_query(db_client, chain_ids)
        
        # 执行订单ID查询
        # order_id_results = execute_order_id_query(db_client, chain_ids)
        
        # 输出结果到文件

        print(f"[INFO] SQL查询执行完成，结果已保存到 {args.output}")
        print(f"[INFO] 生成的更新语句数量: {len(update_results)}")

    except Exception as e:
        print(f"[ERROR] 执行SQL查询失败: {str(e)}")
    finally:
        # 关闭数据库连接
        db_client.close()

if __name__ == "__main__":
    main()
